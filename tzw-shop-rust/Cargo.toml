[package]
name = "tzw-shop-rust"
version = "0.1.0"
edition = "2024"
authors = ["TZW Shop Team"]
description = "TZW电商系统Rust版本"
license = "MIT"

[dependencies]
# Web框架
actix-web = "4.8"
actix-cors = "0.7"
actix-files = "0.6"

async-trait = "0.1"

# 异步运行时
tokio = { version = "1.35", features = ["full"] }

# 数据库
sqlx = { version = "0.7", features = [
    "runtime-tokio-rustls",
    "mysql", 
    "chrono", 
    "uuid",
    "rust_decimal",
    "json"
] }

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# HTTP客户端
reqwest = { version = "0.11", features = ["json", "rustls-tls"] }

# 认证
jsonwebtoken = "9.2"
bcrypt = "0.15"

# 缓存
redis = { version = "0.24", features = ["tokio-comp", "connection-manager"] }
mobc = "0.8"
mobc-redis = "0.8"

# 日志
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
tracing-actix-web = "0.7"

# 错误处理
thiserror = "1.0"
anyhow = "1.0"

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# UUID
uuid = { version = "1.6", features = ["v4", "serde"] }

# 定时任务
tokio-cron-scheduler = "0.10"

# 配置管理
config = "0.14"
dotenvy = "0.15"

# 验证
validator = { version = "0.18", features = ["derive"] }

# 加密
sha2 = "0.10"
hmac = "0.12"

# 数值处理
rust_decimal = { version = "1.33", features = ["serde"] }

# 随机数生成
rand = "0.8"

# 异步工具
futures = "0.3"

# 内存缓存
moka = { version = "0.12", features = ["future"] }

[dev-dependencies]
# 测试
tokio-test = "0.4"
actix-web = { version = "4.8", features = ["macros"] }

# 性能测试
criterion = { version = "0.5", features = ["html_reports"] }

# 测试工具
serial_test = "3.0"

[[bin]]
name = "admin-api"
path = "src/bin/admin-api.rs"

[[bin]]  
name = "wx-api"
path = "src/bin/wx-api.rs"

[[bench]]
name = "order_benchmark"
harness = false

[profile.release]
# 生产环境优化
opt-level = 3
lto = true
panic = "abort"
strip = true

[profile.dev]
# 开发环境优化编译速度
opt-level = 0
debug = true
