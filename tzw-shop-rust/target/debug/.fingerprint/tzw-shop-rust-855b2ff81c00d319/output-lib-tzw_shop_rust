{"$message_type":"diagnostic","message":"unused import: `PageResult`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/services/user_service.rs","byte_start":191,"byte_end":201,"line_start":7,"line_end":7,"column_start":40,"column_end":50,"is_primary":true,"text":[{"text":"use crate::models::common::{PageQuery, PageResult};","highlight_start":40,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/services/user_service.rs","byte_start":189,"byte_end":201,"line_start":7,"line_end":7,"column_start":38,"column_end":50,"is_primary":true,"text":[{"text":"use crate::models::common::{PageQuery, PageResult};","highlight_start":38,"highlight_end":50}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/services/user_service.rs","byte_start":179,"byte_end":180,"line_start":7,"line_end":7,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"use crate::models::common::{PageQuery, PageResult};","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/services/user_service.rs","byte_start":201,"byte_end":202,"line_start":7,"line_end":7,"column_start":50,"column_end":51,"is_primary":true,"text":[{"text":"use crate::models::common::{PageQuery, PageResult};","highlight_start":50,"highlight_end":51}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `PageResult`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/user_service.rs:7:40\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::models::common::{PageQuery, PageResult};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `PageResult`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/services/goods_service.rs","byte_start":263,"byte_end":273,"line_start":7,"line_end":7,"column_start":40,"column_end":50,"is_primary":true,"text":[{"text":"use crate::models::common::{PageQuery, PageResult, ApproveStatus};","highlight_start":40,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/services/goods_service.rs","byte_start":261,"byte_end":273,"line_start":7,"line_end":7,"column_start":38,"column_end":50,"is_primary":true,"text":[{"text":"use crate::models::common::{PageQuery, PageResult, ApproveStatus};","highlight_start":38,"highlight_end":50}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `PageResult`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/goods_service.rs:7:40\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::models::common::{PageQuery, PageResult, ApproveStatus};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `PageResult`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/services/order_service.rs","byte_start":277,"byte_end":287,"line_start":8,"line_end":8,"column_start":40,"column_end":50,"is_primary":true,"text":[{"text":"use crate::models::common::{PageQuery, PageResult, OrderStatus, FreightType};","highlight_start":40,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/services/order_service.rs","byte_start":275,"byte_end":287,"line_start":8,"line_end":8,"column_start":38,"column_end":50,"is_primary":true,"text":[{"text":"use crate::models::common::{PageQuery, PageResult, OrderStatus, FreightType};","highlight_start":38,"highlight_end":50}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `PageResult`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/order_service.rs:8:40\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::models::common::{PageQuery, PageResult, OrderStatus, FreightType};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `PageQuery` and `PageResult`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/services/commission_service.rs","byte_start":198,"byte_end":207,"line_start":7,"line_end":7,"column_start":29,"column_end":38,"is_primary":true,"text":[{"text":"use crate::models::common::{PageQuery, PageResult};","highlight_start":29,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/commission_service.rs","byte_start":209,"byte_end":219,"line_start":7,"line_end":7,"column_start":40,"column_end":50,"is_primary":true,"text":[{"text":"use crate::models::common::{PageQuery, PageResult};","highlight_start":40,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/services/commission_service.rs","byte_start":170,"byte_end":222,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::models::common::{PageQuery, PageResult};","highlight_start":1,"highlight_end":52},{"text":"use crate::repositories::user_repo::UserRepository;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `PageQuery` and `PageResult`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/commission_service.rs:7:29\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::models::common::{PageQuery, PageResult};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `error`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/services/external_service.rs","byte_start":308,"byte_end":313,"line_start":11,"line_end":11,"column_start":21,"column_end":26,"is_primary":true,"text":[{"text":"use tracing::{info, error, instrument};","highlight_start":21,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/services/external_service.rs","byte_start":306,"byte_end":313,"line_start":11,"line_end":11,"column_start":19,"column_end":26,"is_primary":true,"text":[{"text":"use tracing::{info, error, instrument};","highlight_start":19,"highlight_end":26}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `error`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/external_service.rs:11:21\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{info, error, instrument};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `mobile`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/auth_service.rs","byte_start":14561,"byte_end":14567,"line_start":429,"line_end":429,"column_start":37,"column_end":43,"is_primary":true,"text":[{"text":"    async fn verify_sms_code(&self, mobile: &str, code: &str) -> AppResult<()> {","highlight_start":37,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/auth_service.rs","byte_start":14561,"byte_end":14567,"line_start":429,"line_end":429,"column_start":37,"column_end":43,"is_primary":true,"text":[{"text":"    async fn verify_sms_code(&self, mobile: &str, code: &str) -> AppResult<()> {","highlight_start":37,"highlight_end":43}],"label":null,"suggested_replacement":"_mobile","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `mobile`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/auth_service.rs:429:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m429\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn verify_sms_code(&self, mobile: &str, code: &str) -> AppResult<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_mobile`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src/services/order_service.rs","byte_start":7071,"byte_end":7084,"line_start":212,"line_end":212,"column_start":24,"column_end":37,"is_primary":true,"text":[{"text":"                price: price.clone(),","highlight_start":24,"highlight_end":37}],"label":"expected `f64`, found `Decimal`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m: mismatched types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/order_service.rs:212:24\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m212\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                price: price.clone(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `f64`, found `Decimal`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `rust_decimal::Decimal: tracing::Value` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":95816,"byte_end":95821,"line_start":2814,"line_end":2814,"column_start":75,"column_end":80,"is_primary":true,"text":[{"text":"            @ { $($out),*, (&$next, $crate::__macro_support::Option::Some(&$val as &dyn Value)) },","highlight_start":75,"highlight_end":80}],"label":"the trait `tracing::Value` is not implemented for `rust_decimal::Decimal`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":95723,"byte_end":95891,"line_start":2813,"line_end":2817,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"        $crate::valueset!(","highlight_start":9,"highlight_end":27},{"text":"            @ { $($out),*, (&$next, $crate::__macro_support::Option::Some(&$val as &dyn Value)) },","highlight_start":1,"highlight_end":99},{"text":"            $next,","highlight_start":1,"highlight_end":19},{"text":"            $($rest)*","highlight_start":1,"highlight_end":22},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":95723,"byte_end":95891,"line_start":2813,"line_end":2817,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"        $crate::valueset!(","highlight_start":9,"highlight_end":27},{"text":"            @ { $($out),*, (&$next, $crate::__macro_support::Option::Some(&$val as &dyn Value)) },","highlight_start":1,"highlight_end":99},{"text":"            $next,","highlight_start":1,"highlight_end":19},{"text":"            $($rest)*","highlight_start":1,"highlight_end":22},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":101700,"byte_end":101893,"line_start":2970,"line_end":2974,"column_start":31,"column_end":14,"is_primary":false,"text":[{"text":"            $fields.value_set($crate::valueset!(","highlight_start":31,"highlight_end":49},{"text":"                @ { },","highlight_start":1,"highlight_end":23},{"text":"                $crate::__macro_support::Iterator::next(&mut iter).expect(\"FieldSet corrupted (this is a bug)\"),","highlight_start":1,"highlight_end":113},{"text":"                $($kvs)+","highlight_start":1,"highlight_end":25},{"text":"            ))","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":28037,"byte_end":28099,"line_start":884,"line_end":884,"column_start":16,"column_end":78,"is_primary":false,"text":[{"text":"            })($crate::valueset!(__CALLSITE.metadata().fields(), $($fields)*));","highlight_start":16,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":69820,"byte_end":69950,"line_start":2047,"line_end":2051,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"        $crate::event!(","highlight_start":9,"highlight_end":24},{"text":"            target: module_path!(),","highlight_start":1,"highlight_end":36},{"text":"            $crate::Level::INFO,","highlight_start":1,"highlight_end":33},{"text":"            { $($k).+ = $($field)*}","highlight_start":1,"highlight_end":36},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src/services/order_service.rs","byte_start":10141,"byte_end":10245,"line_start":299,"line_end":299,"column_start":9,"column_end":101,"is_primary":false,"text":[{"text":"        info!(order_id = order_id, user_id = request.user_id, actual_price = actual_price, \"订单创建成功\");","highlight_start":9,"highlight_end":101}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"info!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":62152,"byte_end":62169,"line_start":1866,"line_end":1866,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"macro_rules! info {","highlight_start":1,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::event!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":16449,"byte_end":16467,"line_start":585,"line_end":585,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! event {","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::valueset!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":94593,"byte_end":94614,"line_start":2784,"line_end":2784,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! valueset {","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::valueset!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":94593,"byte_end":94614,"line_start":2784,"line_end":2784,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! valueset {","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::valueset!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":94593,"byte_end":94614,"line_start":2784,"line_end":2784,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! valueset {","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::valueset!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":94593,"byte_end":94614,"line_start":2784,"line_end":2784,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! valueset {","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the following other types implement trait `tracing::Value`:\n  &'a T\n  &'a mut T\n  (dyn StdError + 'static)\n  (dyn StdError + Send + 'static)\n  (dyn StdError + Send + Sync + 'static)\n  (dyn StdError + Sync + 'static)\n  Box<T>\n  DebugValue<T>\nand 35 others","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required for the cast from `&rust_decimal::Decimal` to `&dyn tracing::Value`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `rust_decimal::Decimal: tracing::Value` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/order_service.rs:299:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m299\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        info!(order_id = order_id, user_id = request.user_id, actual_price = actual_price, \"订单创建成功\");\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `tracing::Value` is not implemented for `rust_decimal::Decimal`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `tracing::Value`:\u001b[0m\n\u001b[0m              &'a T\u001b[0m\n\u001b[0m              &'a mut T\u001b[0m\n\u001b[0m              (dyn StdError + 'static)\u001b[0m\n\u001b[0m              (dyn StdError + Send + 'static)\u001b[0m\n\u001b[0m              (dyn StdError + Send + Sync + 'static)\u001b[0m\n\u001b[0m              (dyn StdError + Sync + 'static)\u001b[0m\n\u001b[0m              Box<T>\u001b[0m\n\u001b[0m              DebugValue<T>\u001b[0m\n\u001b[0m            and 35 others\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: required for the cast from `&rust_decimal::Decimal` to `&dyn tracing::Value`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::valueset` which comes from the expansion of the macro `info` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `to_i64` found for struct `rust_decimal::Decimal` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/services/payment_service.rs","byte_start":5157,"byte_end":5213,"line_start":172,"line_end":173,"column_start":28,"column_end":14,"is_primary":false,"text":[{"text":"        let order_amount = (&order.actual_price * Decimal::from(100))","highlight_start":28,"highlight_end":70},{"text":"            .to_i64()","highlight_start":1,"highlight_end":14}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/num-traits-0.2.19/src/cast.rs","byte_start":1895,"byte_end":1901,"line_start":48,"line_end":48,"column_start":8,"column_end":14,"is_primary":false,"text":[{"text":"    fn to_i64(&self) -> Option<i64>;","highlight_start":8,"highlight_end":14}],"label":"the method is available for `rust_decimal::Decimal` here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/payment_service.rs","byte_start":5213,"byte_end":5219,"line_start":173,"line_end":173,"column_start":14,"column_end":20,"is_primary":true,"text":[{"text":"            .to_i64()","highlight_start":14,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"items from traits can only be used if the trait is in scope","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"trait `ToPrimitive` which provides `to_i64` is implemented but not in scope; perhaps you want to import it","code":null,"level":"help","spans":[{"file_name":"src/services/payment_service.rs","byte_start":117,"byte_end":117,"line_start":5,"line_end":5,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_decimal::Decimal;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use rust_decimal::prelude::ToPrimitive;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"there is a method `to_f64` with a similar name","code":null,"level":"help","spans":[{"file_name":"src/services/payment_service.rs","byte_start":5213,"byte_end":5219,"line_start":173,"line_end":173,"column_start":14,"column_end":20,"is_primary":true,"text":[{"text":"            .to_i64()","highlight_start":14,"highlight_end":20}],"label":null,"suggested_replacement":"to_f64","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `to_i64` found for struct `rust_decimal::Decimal` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/payment_service.rs:173:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m172\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        let order_amount = (&order.actual_price * Decimal::from(100))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m ____________________________-\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m173\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .to_i64()\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|_____________-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/num-traits-0.2.19/src/cast.rs:48:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m48\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m    fn to_i64(&self) -> Option<i64>;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthe method is available for `rust_decimal::Decimal` here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: items from traits can only be used if the trait is in scope\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: trait `ToPrimitive` which provides `to_i64` is implemented but not in scope; perhaps you want to import it\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use rust_decimal::prelude::ToPrimitive;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `to_f64` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m173\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            .\u001b[0m\u001b[0m\u001b[38;5;9mto_i64\u001b[0m\u001b[0m()\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m173\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            .\u001b[0m\u001b[0m\u001b[38;5;10mto_f64\u001b[0m\u001b[0m()\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `to_i64` found for struct `rust_decimal::Decimal` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/services/payment_service.rs","byte_start":7792,"byte_end":7848,"line_start":232,"line_end":233,"column_start":28,"column_end":14,"is_primary":false,"text":[{"text":"        let order_amount = (&order.actual_price * Decimal::from(100))","highlight_start":28,"highlight_end":70},{"text":"            .to_i64()","highlight_start":1,"highlight_end":14}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/num-traits-0.2.19/src/cast.rs","byte_start":1895,"byte_end":1901,"line_start":48,"line_end":48,"column_start":8,"column_end":14,"is_primary":false,"text":[{"text":"    fn to_i64(&self) -> Option<i64>;","highlight_start":8,"highlight_end":14}],"label":"the method is available for `rust_decimal::Decimal` here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/payment_service.rs","byte_start":7848,"byte_end":7854,"line_start":233,"line_end":233,"column_start":14,"column_end":20,"is_primary":true,"text":[{"text":"            .to_i64()","highlight_start":14,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"items from traits can only be used if the trait is in scope","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"trait `ToPrimitive` which provides `to_i64` is implemented but not in scope; perhaps you want to import it","code":null,"level":"help","spans":[{"file_name":"src/services/payment_service.rs","byte_start":117,"byte_end":117,"line_start":5,"line_end":5,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_decimal::Decimal;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use rust_decimal::prelude::ToPrimitive;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"there is a method `to_f64` with a similar name","code":null,"level":"help","spans":[{"file_name":"src/services/payment_service.rs","byte_start":7848,"byte_end":7854,"line_start":233,"line_end":233,"column_start":14,"column_end":20,"is_primary":true,"text":[{"text":"            .to_i64()","highlight_start":14,"highlight_end":20}],"label":null,"suggested_replacement":"to_f64","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `to_i64` found for struct `rust_decimal::Decimal` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/payment_service.rs:233:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m232\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        let order_amount = (&order.actual_price * Decimal::from(100))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m ____________________________-\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m233\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .to_i64()\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|_____________-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/num-traits-0.2.19/src/cast.rs:48:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m48\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m    fn to_i64(&self) -> Option<i64>;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthe method is available for `rust_decimal::Decimal` here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: items from traits can only be used if the trait is in scope\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: trait `ToPrimitive` which provides `to_i64` is implemented but not in scope; perhaps you want to import it\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use rust_decimal::prelude::ToPrimitive;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `to_f64` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m233\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            .\u001b[0m\u001b[0m\u001b[38;5;9mto_i64\u001b[0m\u001b[0m()\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m233\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            .\u001b[0m\u001b[0m\u001b[38;5;10mto_f64\u001b[0m\u001b[0m()\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `request`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/payment_service.rs","byte_start":8692,"byte_end":8699,"line_start":254,"line_end":254,"column_start":58,"column_end":65,"is_primary":true,"text":[{"text":"    async fn create_wechat_payment(&self, order: &Order, request: &PaymentRequest) -> AppResult<PaymentResponse> {","highlight_start":58,"highlight_end":65}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/payment_service.rs","byte_start":8692,"byte_end":8699,"line_start":254,"line_end":254,"column_start":58,"column_end":65,"is_primary":true,"text":[{"text":"    async fn create_wechat_payment(&self, order: &Order, request: &PaymentRequest) -> AppResult<PaymentResponse> {","highlight_start":58,"highlight_end":65}],"label":null,"suggested_replacement":"_request","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `request`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/payment_service.rs:254:58\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m254\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn create_wechat_payment(&self, order: &Order, request: &PaymentRequest) -> AppResult<PaymentResponse> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_request`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `request`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/payment_service.rs","byte_start":9869,"byte_end":9876,"line_start":280,"line_end":280,"column_start":58,"column_end":65,"is_primary":true,"text":[{"text":"    async fn create_alipay_payment(&self, order: &Order, request: &PaymentRequest) -> AppResult<PaymentResponse> {","highlight_start":58,"highlight_end":65}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/payment_service.rs","byte_start":9869,"byte_end":9876,"line_start":280,"line_end":280,"column_start":58,"column_end":65,"is_primary":true,"text":[{"text":"    async fn create_alipay_payment(&self, order: &Order, request: &PaymentRequest) -> AppResult<PaymentResponse> {","highlight_start":58,"highlight_end":65}],"label":null,"suggested_replacement":"_request","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `request`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/payment_service.rs:280:58\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m280\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn create_alipay_payment(&self, order: &Order, request: &PaymentRequest) -> AppResult<PaymentResponse> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_request`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `request`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/payment_service.rs","byte_start":11212,"byte_end":11219,"line_start":307,"line_end":307,"column_start":59,"column_end":66,"is_primary":true,"text":[{"text":"    async fn create_balance_payment(&self, order: &Order, request: &PaymentRequest) -> AppResult<PaymentResponse> {","highlight_start":59,"highlight_end":66}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/payment_service.rs","byte_start":11212,"byte_end":11219,"line_start":307,"line_end":307,"column_start":59,"column_end":66,"is_primary":true,"text":[{"text":"    async fn create_balance_payment(&self, order: &Order, request: &PaymentRequest) -> AppResult<PaymentResponse> {","highlight_start":59,"highlight_end":66}],"label":null,"suggested_replacement":"_request","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `request`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/payment_service.rs:307:59\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m307\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn create_balance_payment(&self, order: &Order, request: &PaymentRequest) -> AppResult<PaymentResponse> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_request`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `referrer`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/commission_service.rs","byte_start":3724,"byte_end":3732,"line_start":134,"line_end":134,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"        let referrer = self.user_service.find_by_id(referrer_id).await?","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/commission_service.rs","byte_start":3724,"byte_end":3732,"line_start":134,"line_end":134,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"        let referrer = self.user_service.find_by_id(referrer_id).await?","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":"_referrer","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `referrer`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/commission_service.rs:134:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m134\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let referrer = self.user_service.find_by_id(referrer_id).await?\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_referrer`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `referee`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/commission_service.rs","byte_start":3900,"byte_end":3907,"line_start":137,"line_end":137,"column_start":13,"column_end":20,"is_primary":true,"text":[{"text":"        let referee = self.user_service.find_by_id(referee_id).await?","highlight_start":13,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/commission_service.rs","byte_start":3900,"byte_end":3907,"line_start":137,"line_end":137,"column_start":13,"column_end":20,"is_primary":true,"text":[{"text":"        let referee = self.user_service.find_by_id(referee_id).await?","highlight_start":13,"highlight_end":20}],"label":null,"suggested_replacement":"_referee","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `referee`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/commission_service.rs:137:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m137\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let referee = self.user_service.find_by_id(referee_id).await?\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_referee`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `rust_decimal::Decimal: tracing::Value` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":95816,"byte_end":95821,"line_start":2814,"line_end":2814,"column_start":75,"column_end":80,"is_primary":true,"text":[{"text":"            @ { $($out),*, (&$next, $crate::__macro_support::Option::Some(&$val as &dyn Value)) },","highlight_start":75,"highlight_end":80}],"label":"the trait `tracing::Value` is not implemented for `rust_decimal::Decimal`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":95723,"byte_end":95891,"line_start":2813,"line_end":2817,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"        $crate::valueset!(","highlight_start":9,"highlight_end":27},{"text":"            @ { $($out),*, (&$next, $crate::__macro_support::Option::Some(&$val as &dyn Value)) },","highlight_start":1,"highlight_end":99},{"text":"            $next,","highlight_start":1,"highlight_end":19},{"text":"            $($rest)*","highlight_start":1,"highlight_end":22},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":101700,"byte_end":101893,"line_start":2970,"line_end":2974,"column_start":31,"column_end":14,"is_primary":false,"text":[{"text":"            $fields.value_set($crate::valueset!(","highlight_start":31,"highlight_end":49},{"text":"                @ { },","highlight_start":1,"highlight_end":23},{"text":"                $crate::__macro_support::Iterator::next(&mut iter).expect(\"FieldSet corrupted (this is a bug)\"),","highlight_start":1,"highlight_end":113},{"text":"                $($kvs)+","highlight_start":1,"highlight_end":25},{"text":"            ))","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":28037,"byte_end":28099,"line_start":884,"line_end":884,"column_start":16,"column_end":78,"is_primary":false,"text":[{"text":"            })($crate::valueset!(__CALLSITE.metadata().fields(), $($fields)*));","highlight_start":16,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":69820,"byte_end":69950,"line_start":2047,"line_end":2051,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"        $crate::event!(","highlight_start":9,"highlight_end":24},{"text":"            target: module_path!(),","highlight_start":1,"highlight_end":36},{"text":"            $crate::Level::INFO,","highlight_start":1,"highlight_end":33},{"text":"            { $($k).+ = $($field)*}","highlight_start":1,"highlight_end":36},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src/services/commission_service.rs","byte_start":8049,"byte_end":8134,"line_start":248,"line_end":248,"column_start":9,"column_end":78,"is_primary":false,"text":[{"text":"        info!(user_id = request.user_id, amount = request.amount, \"佣金结算申请成功\");","highlight_start":9,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"info!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":62152,"byte_end":62169,"line_start":1866,"line_end":1866,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"macro_rules! info {","highlight_start":1,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::event!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":16449,"byte_end":16467,"line_start":585,"line_end":585,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! event {","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::valueset!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":94593,"byte_end":94614,"line_start":2784,"line_end":2784,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! valueset {","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::valueset!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":94593,"byte_end":94614,"line_start":2784,"line_end":2784,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! valueset {","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::valueset!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":94593,"byte_end":94614,"line_start":2784,"line_end":2784,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! valueset {","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the following other types implement trait `tracing::Value`:\n  &'a T\n  &'a mut T\n  (dyn StdError + 'static)\n  (dyn StdError + Send + 'static)\n  (dyn StdError + Send + Sync + 'static)\n  (dyn StdError + Sync + 'static)\n  Box<T>\n  DebugValue<T>\nand 35 others","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required for the cast from `&rust_decimal::Decimal` to `&dyn tracing::Value`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `rust_decimal::Decimal: tracing::Value` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/commission_service.rs:248:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m248\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        info!(user_id = request.user_id, amount = request.amount, \"佣金结算申请成功\");\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `tracing::Value` is not implemented for `rust_decimal::Decimal`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `tracing::Value`:\u001b[0m\n\u001b[0m              &'a T\u001b[0m\n\u001b[0m              &'a mut T\u001b[0m\n\u001b[0m              (dyn StdError + 'static)\u001b[0m\n\u001b[0m              (dyn StdError + Send + 'static)\u001b[0m\n\u001b[0m              (dyn StdError + Send + Sync + 'static)\u001b[0m\n\u001b[0m              (dyn StdError + Sync + 'static)\u001b[0m\n\u001b[0m              Box<T>\u001b[0m\n\u001b[0m              DebugValue<T>\u001b[0m\n\u001b[0m            and 35 others\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: required for the cast from `&rust_decimal::Decimal` to `&dyn tracing::Value`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::valueset` which comes from the expansion of the macro `info` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `user_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/commission_service.rs","byte_start":8232,"byte_end":8239,"line_start":253,"line_end":253,"column_start":45,"column_end":52,"is_primary":true,"text":[{"text":"    async fn get_user_referral_chain(&self, user_id: i32) -> AppResult<Vec<ReferralRelation>> {","highlight_start":45,"highlight_end":52}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/commission_service.rs","byte_start":8232,"byte_end":8239,"line_start":253,"line_end":253,"column_start":45,"column_end":52,"is_primary":true,"text":[{"text":"    async fn get_user_referral_chain(&self, user_id: i32) -> AppResult<Vec<ReferralRelation>> {","highlight_start":45,"highlight_end":52}],"label":null,"suggested_replacement":"_user_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `user_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/commission_service.rs:253:45\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m253\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_user_referral_chain(&self, user_id: i32) -> AppResult<Vec<ReferralRelation>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_user_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `rust_decimal::Decimal: tracing::Value` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":95816,"byte_end":95821,"line_start":2814,"line_end":2814,"column_start":75,"column_end":80,"is_primary":true,"text":[{"text":"            @ { $($out),*, (&$next, $crate::__macro_support::Option::Some(&$val as &dyn Value)) },","highlight_start":75,"highlight_end":80}],"label":"the trait `tracing::Value` is not implemented for `rust_decimal::Decimal`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":95723,"byte_end":95891,"line_start":2813,"line_end":2817,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"        $crate::valueset!(","highlight_start":9,"highlight_end":27},{"text":"            @ { $($out),*, (&$next, $crate::__macro_support::Option::Some(&$val as &dyn Value)) },","highlight_start":1,"highlight_end":99},{"text":"            $next,","highlight_start":1,"highlight_end":19},{"text":"            $($rest)*","highlight_start":1,"highlight_end":22},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":95723,"byte_end":95891,"line_start":2813,"line_end":2817,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"        $crate::valueset!(","highlight_start":9,"highlight_end":27},{"text":"            @ { $($out),*, (&$next, $crate::__macro_support::Option::Some(&$val as &dyn Value)) },","highlight_start":1,"highlight_end":99},{"text":"            $next,","highlight_start":1,"highlight_end":19},{"text":"            $($rest)*","highlight_start":1,"highlight_end":22},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":101700,"byte_end":101893,"line_start":2970,"line_end":2974,"column_start":31,"column_end":14,"is_primary":false,"text":[{"text":"            $fields.value_set($crate::valueset!(","highlight_start":31,"highlight_end":49},{"text":"                @ { },","highlight_start":1,"highlight_end":23},{"text":"                $crate::__macro_support::Iterator::next(&mut iter).expect(\"FieldSet corrupted (this is a bug)\"),","highlight_start":1,"highlight_end":113},{"text":"                $($kvs)+","highlight_start":1,"highlight_end":25},{"text":"            ))","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":28037,"byte_end":28099,"line_start":884,"line_end":884,"column_start":16,"column_end":78,"is_primary":false,"text":[{"text":"            })($crate::valueset!(__CALLSITE.metadata().fields(), $($fields)*));","highlight_start":16,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":69820,"byte_end":69950,"line_start":2047,"line_end":2051,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"        $crate::event!(","highlight_start":9,"highlight_end":24},{"text":"            target: module_path!(),","highlight_start":1,"highlight_end":36},{"text":"            $crate::Level::INFO,","highlight_start":1,"highlight_end":33},{"text":"            { $($k).+ = $($field)*}","highlight_start":1,"highlight_end":36},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src/services/commission_service.rs","byte_start":9221,"byte_end":9440,"line_start":282,"line_end":288,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"        info!(","highlight_start":9,"highlight_end":15},{"text":"            user_id = user_id,","highlight_start":1,"highlight_end":31},{"text":"            referral_user_id = referral_user_id,","highlight_start":1,"highlight_end":49},{"text":"            order_id = order_id,","highlight_start":1,"highlight_end":33},{"text":"            commission_amount = commission_amount,","highlight_start":1,"highlight_end":51},{"text":"            \"佣金记录创建成功\"","highlight_start":1,"highlight_end":23},{"text":"        );","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"info!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":62152,"byte_end":62169,"line_start":1866,"line_end":1866,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"macro_rules! info {","highlight_start":1,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::event!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":16449,"byte_end":16467,"line_start":585,"line_end":585,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! event {","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::valueset!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":94593,"byte_end":94614,"line_start":2784,"line_end":2784,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! valueset {","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::valueset!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":94593,"byte_end":94614,"line_start":2784,"line_end":2784,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! valueset {","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::valueset!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":94593,"byte_end":94614,"line_start":2784,"line_end":2784,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! valueset {","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::valueset!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/tracing-0.1.41/src/macros.rs","byte_start":94593,"byte_end":94614,"line_start":2784,"line_end":2784,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! valueset {","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the following other types implement trait `tracing::Value`:\n  &'a T\n  &'a mut T\n  (dyn StdError + 'static)\n  (dyn StdError + Send + 'static)\n  (dyn StdError + Send + Sync + 'static)\n  (dyn StdError + Sync + 'static)\n  Box<T>\n  DebugValue<T>\nand 35 others","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required for the cast from `&rust_decimal::Decimal` to `&dyn tracing::Value`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `rust_decimal::Decimal: tracing::Value` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/commission_service.rs:282:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m282\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m        info!(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m283\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            user_id = user_id,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m284\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            referral_user_id = referral_user_id,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m285\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            order_id = order_id,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m286\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            commission_amount = commission_amount,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m287\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            \"佣金记录创建成功\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m288\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        );\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `tracing::Value` is not implemented for `rust_decimal::Decimal`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `tracing::Value`:\u001b[0m\n\u001b[0m              &'a T\u001b[0m\n\u001b[0m              &'a mut T\u001b[0m\n\u001b[0m              (dyn StdError + 'static)\u001b[0m\n\u001b[0m              (dyn StdError + Send + 'static)\u001b[0m\n\u001b[0m              (dyn StdError + Send + Sync + 'static)\u001b[0m\n\u001b[0m              (dyn StdError + Sync + 'static)\u001b[0m\n\u001b[0m              Box<T>\u001b[0m\n\u001b[0m              DebugValue<T>\u001b[0m\n\u001b[0m            and 35 others\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: required for the cast from `&rust_decimal::Decimal` to `&dyn tracing::Value`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::valueset` which comes from the expansion of the macro `info` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `referrer_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/commission_service.rs","byte_start":9554,"byte_end":9565,"line_start":293,"line_end":293,"column_start":52,"column_end":63,"is_primary":true,"text":[{"text":"    async fn check_referral_relation_exists(&self, referrer_id: i32, referee_id: i32) -> AppResult<bool> {","highlight_start":52,"highlight_end":63}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/commission_service.rs","byte_start":9554,"byte_end":9565,"line_start":293,"line_end":293,"column_start":52,"column_end":63,"is_primary":true,"text":[{"text":"    async fn check_referral_relation_exists(&self, referrer_id: i32, referee_id: i32) -> AppResult<bool> {","highlight_start":52,"highlight_end":63}],"label":null,"suggested_replacement":"_referrer_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `referrer_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/commission_service.rs:293:52\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m293\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn check_referral_relation_exists(&self, referrer_id: i32, referee_id: i32) -> AppResult<bool> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_referrer_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `referee_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/commission_service.rs","byte_start":9572,"byte_end":9582,"line_start":293,"line_end":293,"column_start":70,"column_end":80,"is_primary":true,"text":[{"text":"    async fn check_referral_relation_exists(&self, referrer_id: i32, referee_id: i32) -> AppResult<bool> {","highlight_start":70,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/commission_service.rs","byte_start":9572,"byte_end":9582,"line_start":293,"line_end":293,"column_start":70,"column_end":80,"is_primary":true,"text":[{"text":"    async fn check_referral_relation_exists(&self, referrer_id: i32, referee_id: i32) -> AppResult<bool> {","highlight_start":70,"highlight_end":80}],"label":null,"suggested_replacement":"_referee_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `referee_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/commission_service.rs:293:70\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m293\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn check_referral_relation_exists(&self, referrer_id: i32, referee_id: i32) -> AppResult<bool> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_referee_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `referrer_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/commission_service.rs","byte_start":9760,"byte_end":9771,"line_start":299,"line_end":299,"column_start":45,"column_end":56,"is_primary":true,"text":[{"text":"    async fn check_circular_referral(&self, referrer_id: i32, referee_id: i32) -> AppResult<bool> {","highlight_start":45,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/commission_service.rs","byte_start":9760,"byte_end":9771,"line_start":299,"line_end":299,"column_start":45,"column_end":56,"is_primary":true,"text":[{"text":"    async fn check_circular_referral(&self, referrer_id: i32, referee_id: i32) -> AppResult<bool> {","highlight_start":45,"highlight_end":56}],"label":null,"suggested_replacement":"_referrer_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `referrer_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/commission_service.rs:299:45\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m299\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn check_circular_referral(&self, referrer_id: i32, referee_id: i32) -> AppResult<bool> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_referrer_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `referee_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/commission_service.rs","byte_start":9778,"byte_end":9788,"line_start":299,"line_end":299,"column_start":63,"column_end":73,"is_primary":true,"text":[{"text":"    async fn check_circular_referral(&self, referrer_id: i32, referee_id: i32) -> AppResult<bool> {","highlight_start":63,"highlight_end":73}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/commission_service.rs","byte_start":9778,"byte_end":9788,"line_start":299,"line_end":299,"column_start":63,"column_end":73,"is_primary":true,"text":[{"text":"    async fn check_circular_referral(&self, referrer_id: i32, referee_id: i32) -> AppResult<bool> {","highlight_start":63,"highlight_end":73}],"label":null,"suggested_replacement":"_referee_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `referee_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/commission_service.rs:299:63\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m299\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn check_circular_referral(&self, referrer_id: i32, referee_id: i32) -> AppResult<bool> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_referee_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `referrer_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/commission_service.rs","byte_start":9955,"byte_end":9966,"line_start":305,"line_end":305,"column_start":46,"column_end":57,"is_primary":true,"text":[{"text":"    async fn insert_referral_relation(&self, referrer_id: i32, referee_id: i32, level: i8) -> AppResult<()> {","highlight_start":46,"highlight_end":57}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/commission_service.rs","byte_start":9955,"byte_end":9966,"line_start":305,"line_end":305,"column_start":46,"column_end":57,"is_primary":true,"text":[{"text":"    async fn insert_referral_relation(&self, referrer_id: i32, referee_id: i32, level: i8) -> AppResult<()> {","highlight_start":46,"highlight_end":57}],"label":null,"suggested_replacement":"_referrer_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `referrer_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/commission_service.rs:305:46\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m305\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn insert_referral_relation(&self, referrer_id: i32, referee_id: i32, level: i8) -> AppResult<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_referrer_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `referee_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/commission_service.rs","byte_start":9973,"byte_end":9983,"line_start":305,"line_end":305,"column_start":64,"column_end":74,"is_primary":true,"text":[{"text":"    async fn insert_referral_relation(&self, referrer_id: i32, referee_id: i32, level: i8) -> AppResult<()> {","highlight_start":64,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/commission_service.rs","byte_start":9973,"byte_end":9983,"line_start":305,"line_end":305,"column_start":64,"column_end":74,"is_primary":true,"text":[{"text":"    async fn insert_referral_relation(&self, referrer_id: i32, referee_id: i32, level: i8) -> AppResult<()> {","highlight_start":64,"highlight_end":74}],"label":null,"suggested_replacement":"_referee_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `referee_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/commission_service.rs:305:64\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m305\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn insert_referral_relation(&self, referrer_id: i32, referee_id: i32, level: i8) -> AppResult<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_referee_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `level`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/commission_service.rs","byte_start":9990,"byte_end":9995,"line_start":305,"line_end":305,"column_start":81,"column_end":86,"is_primary":true,"text":[{"text":"    async fn insert_referral_relation(&self, referrer_id: i32, referee_id: i32, level: i8) -> AppResult<()> {","highlight_start":81,"highlight_end":86}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/commission_service.rs","byte_start":9990,"byte_end":9995,"line_start":305,"line_end":305,"column_start":81,"column_end":86,"is_primary":true,"text":[{"text":"    async fn insert_referral_relation(&self, referrer_id: i32, referee_id: i32, level: i8) -> AppResult<()> {","highlight_start":81,"highlight_end":86}],"label":null,"suggested_replacement":"_level","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `level`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/commission_service.rs:305:81\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m305\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn insert_referral_relation(&self, referrer_id: i32, referee_id: i32, level: i8) -> AppResult<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_level`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `user_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/commission_service.rs","byte_start":10168,"byte_end":10175,"line_start":311,"line_end":311,"column_start":49,"column_end":56,"is_primary":true,"text":[{"text":"    async fn get_user_pending_commission(&self, user_id: i32) -> AppResult<Decimal> {","highlight_start":49,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/commission_service.rs","byte_start":10168,"byte_end":10175,"line_start":311,"line_end":311,"column_start":49,"column_end":56,"is_primary":true,"text":[{"text":"    async fn get_user_pending_commission(&self, user_id: i32) -> AppResult<Decimal> {","highlight_start":49,"highlight_end":56}],"label":null,"suggested_replacement":"_user_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `user_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/commission_service.rs:311:49\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m311\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_user_pending_commission(&self, user_id: i32) -> AppResult<Decimal> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_user_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the method `validate_range` exists for reference `&Decimal`, but its trait bounds were not satisfied","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/rust_decimal-1.37.2/src/decimal.rs","byte_start":2944,"byte_end":2962,"line_start":115,"line_end":115,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"pub struct Decimal {","highlight_start":1,"highlight_end":19}],"label":"doesn't satisfy `_: ValidateRange<Decimal>` or `_: ValidateRangeType`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/goods_service.rs","byte_start":706,"byte_end":725,"line_start":20,"line_end":20,"column_start":37,"column_end":56,"is_primary":true,"text":[{"text":"#[derive(Debug, serde::Deserialize, validator::Validate)]","highlight_start":37,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src/services/goods_service.rs","byte_start":706,"byte_end":725,"line_start":20,"line_end":20,"column_start":37,"column_end":56,"is_primary":false,"text":[{"text":"#[derive(Debug, serde::Deserialize, validator::Validate)]","highlight_start":37,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[derive(validator::Validate)]","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/validator_derive-0.18.2/src/lib.rs","byte_start":9609,"byte_end":9628,"line_start":303,"line_end":303,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"#[proc_macro_error]","highlight_start":1,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the following trait bounds were not satisfied:\n`rust_decimal::Decimal: validator::validation::range::ValidateRangeType`\nwhich is required by `rust_decimal::Decimal: ValidateRange<rust_decimal::Decimal>`\n`&rust_decimal::Decimal: validator::validation::range::ValidateRangeType`\nwhich is required by `&rust_decimal::Decimal: ValidateRange<&rust_decimal::Decimal>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: the method `validate_range` exists for reference `&Decimal`, but its trait bounds were not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/goods_service.rs:20:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m20\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[derive(Debug, serde::Deserialize, validator::Validate)]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/rust_decimal-1.37.2/src/decimal.rs:115:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m115\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct Decimal {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mdoesn't satisfy `_: ValidateRange<Decimal>` or `_: ValidateRangeType`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the following trait bounds were not satisfied:\u001b[0m\n\u001b[0m            `rust_decimal::Decimal: validator::validation::range::ValidateRangeType`\u001b[0m\n\u001b[0m            which is required by `rust_decimal::Decimal: ValidateRange<rust_decimal::Decimal>`\u001b[0m\n\u001b[0m            `&rust_decimal::Decimal: validator::validation::range::ValidateRangeType`\u001b[0m\n\u001b[0m            which is required by `&rust_decimal::Decimal: ValidateRange<&rust_decimal::Decimal>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this error originates in the derive macro `validator::Validate` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the method `validate_range` exists for struct `Decimal`, but its trait bounds were not satisfied","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/rust_decimal-1.37.2/src/decimal.rs","byte_start":2944,"byte_end":2962,"line_start":115,"line_end":115,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"pub struct Decimal {","highlight_start":1,"highlight_end":19}],"label":"doesn't satisfy `_: ValidateRange<Decimal>` or `_: ValidateRangeType`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/commission_service.rs","byte_start":2532,"byte_end":2551,"line_start":94,"line_end":94,"column_start":37,"column_end":56,"is_primary":true,"text":[{"text":"#[derive(Debug, Clone, Deserialize, validator::Validate)]","highlight_start":37,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src/services/commission_service.rs","byte_start":2532,"byte_end":2551,"line_start":94,"line_end":94,"column_start":37,"column_end":56,"is_primary":false,"text":[{"text":"#[derive(Debug, Clone, Deserialize, validator::Validate)]","highlight_start":37,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[derive(validator::Validate)]","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/validator_derive-0.18.2/src/lib.rs","byte_start":9609,"byte_end":9628,"line_start":303,"line_end":303,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"#[proc_macro_error]","highlight_start":1,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the following trait bounds were not satisfied:\n`rust_decimal::Decimal: validator::validation::range::ValidateRangeType`\nwhich is required by `rust_decimal::Decimal: ValidateRange<rust_decimal::Decimal>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: the method `validate_range` exists for struct `Decimal`, but its trait bounds were not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/commission_service.rs:94:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m94\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[derive(Debug, Clone, Deserialize, validator::Validate)]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-38d0e5eb5da2abae/rust_decimal-1.37.2/src/decimal.rs:115:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m115\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct Decimal {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mdoesn't satisfy `_: ValidateRange<Decimal>` or `_: ValidateRangeType`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the following trait bounds were not satisfied:\u001b[0m\n\u001b[0m            `rust_decimal::Decimal: validator::validation::range::ValidateRangeType`\u001b[0m\n\u001b[0m            which is required by `rust_decimal::Decimal: ValidateRange<rust_decimal::Decimal>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this error originates in the derive macro `validator::Validate` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 8 previous errors; 20 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 8 previous errors; 20 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0277, E0308, E0599.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mSome errors have detailed explanations: E0277, E0308, E0599.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0277`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about an error, try `rustc --explain E0277`.\u001b[0m\n"}
