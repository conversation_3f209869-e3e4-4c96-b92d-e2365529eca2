{"$message_type":"diagnostic","message":"unused import: `PageResult`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/services/user_service.rs","byte_start":191,"byte_end":201,"line_start":7,"line_end":7,"column_start":40,"column_end":50,"is_primary":true,"text":[{"text":"use crate::models::common::{PageQuery, PageResult};","highlight_start":40,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/services/user_service.rs","byte_start":189,"byte_end":201,"line_start":7,"line_end":7,"column_start":38,"column_end":50,"is_primary":true,"text":[{"text":"use crate::models::common::{PageQuery, PageResult};","highlight_start":38,"highlight_end":50}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/services/user_service.rs","byte_start":179,"byte_end":180,"line_start":7,"line_end":7,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"use crate::models::common::{PageQuery, PageResult};","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/services/user_service.rs","byte_start":201,"byte_end":202,"line_start":7,"line_end":7,"column_start":50,"column_end":51,"is_primary":true,"text":[{"text":"use crate::models::common::{PageQuery, PageResult};","highlight_start":50,"highlight_end":51}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `PageResult`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/user_service.rs:7:40\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::models::common::{PageQuery, PageResult};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `PageResult`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/services/goods_service.rs","byte_start":263,"byte_end":273,"line_start":7,"line_end":7,"column_start":40,"column_end":50,"is_primary":true,"text":[{"text":"use crate::models::common::{PageQuery, PageResult, ApproveStatus};","highlight_start":40,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/services/goods_service.rs","byte_start":261,"byte_end":273,"line_start":7,"line_end":7,"column_start":38,"column_end":50,"is_primary":true,"text":[{"text":"use crate::models::common::{PageQuery, PageResult, ApproveStatus};","highlight_start":38,"highlight_end":50}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `PageResult`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/goods_service.rs:7:40\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::models::common::{PageQuery, PageResult, ApproveStatus};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `PageResult`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/services/order_service.rs","byte_start":251,"byte_end":261,"line_start":7,"line_end":7,"column_start":40,"column_end":50,"is_primary":true,"text":[{"text":"use crate::models::common::{PageQuery, PageResult, OrderStatus, FreightType};","highlight_start":40,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/services/order_service.rs","byte_start":249,"byte_end":261,"line_start":7,"line_end":7,"column_start":38,"column_end":50,"is_primary":true,"text":[{"text":"use crate::models::common::{PageQuery, PageResult, OrderStatus, FreightType};","highlight_start":38,"highlight_end":50}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `PageResult`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/order_service.rs:7:40\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::models::common::{PageQuery, PageResult, OrderStatus, FreightType};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `PageQuery` and `PageResult`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/services/commission_service.rs","byte_start":172,"byte_end":181,"line_start":6,"line_end":6,"column_start":29,"column_end":38,"is_primary":true,"text":[{"text":"use crate::models::common::{PageQuery, PageResult};","highlight_start":29,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/commission_service.rs","byte_start":183,"byte_end":193,"line_start":6,"line_end":6,"column_start":40,"column_end":50,"is_primary":true,"text":[{"text":"use crate::models::common::{PageQuery, PageResult};","highlight_start":40,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/services/commission_service.rs","byte_start":144,"byte_end":196,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::models::common::{PageQuery, PageResult};","highlight_start":1,"highlight_end":52},{"text":"use crate::repositories::user_repo::UserRepository;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `PageQuery` and `PageResult`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/commission_service.rs:6:29\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::models::common::{PageQuery, PageResult};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `error`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/services/external_service.rs","byte_start":308,"byte_end":313,"line_start":11,"line_end":11,"column_start":21,"column_end":26,"is_primary":true,"text":[{"text":"use tracing::{info, error, instrument};","highlight_start":21,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/services/external_service.rs","byte_start":306,"byte_end":313,"line_start":11,"line_end":11,"column_start":19,"column_end":26,"is_primary":true,"text":[{"text":"use tracing::{info, error, instrument};","highlight_start":19,"highlight_end":26}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `error`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/external_service.rs:11:21\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{info, error, instrument};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `mobile`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/auth_service.rs","byte_start":14561,"byte_end":14567,"line_start":429,"line_end":429,"column_start":37,"column_end":43,"is_primary":true,"text":[{"text":"    async fn verify_sms_code(&self, mobile: &str, code: &str) -> AppResult<()> {","highlight_start":37,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/auth_service.rs","byte_start":14561,"byte_end":14567,"line_start":429,"line_end":429,"column_start":37,"column_end":43,"is_primary":true,"text":[{"text":"    async fn verify_sms_code(&self, mobile: &str, code: &str) -> AppResult<()> {","highlight_start":37,"highlight_end":43}],"label":null,"suggested_replacement":"_mobile","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `mobile`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/auth_service.rs:429:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m429\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn verify_sms_code(&self, mobile: &str, code: &str) -> AppResult<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_mobile`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `user`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/order_service.rs","byte_start":5512,"byte_end":5516,"line_start":177,"line_end":177,"column_start":13,"column_end":17,"is_primary":true,"text":[{"text":"        let user = self.user_service.find_by_id(request.user_id).await?","highlight_start":13,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/order_service.rs","byte_start":5512,"byte_end":5516,"line_start":177,"line_end":177,"column_start":13,"column_end":17,"is_primary":true,"text":[{"text":"        let user = self.user_service.find_by_id(request.user_id).await?","highlight_start":13,"highlight_end":17}],"label":null,"suggested_replacement":"_user","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `user`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/order_service.rs:177:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m177\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let user = self.user_service.find_by_id(request.user_id).await?\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_user`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `request`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/payment_service.rs","byte_start":8422,"byte_end":8429,"line_start":249,"line_end":249,"column_start":58,"column_end":65,"is_primary":true,"text":[{"text":"    async fn create_wechat_payment(&self, order: &Order, request: &PaymentRequest) -> AppResult<PaymentResponse> {","highlight_start":58,"highlight_end":65}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/payment_service.rs","byte_start":8422,"byte_end":8429,"line_start":249,"line_end":249,"column_start":58,"column_end":65,"is_primary":true,"text":[{"text":"    async fn create_wechat_payment(&self, order: &Order, request: &PaymentRequest) -> AppResult<PaymentResponse> {","highlight_start":58,"highlight_end":65}],"label":null,"suggested_replacement":"_request","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `request`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/payment_service.rs:249:58\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m249\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn create_wechat_payment(&self, order: &Order, request: &PaymentRequest) -> AppResult<PaymentResponse> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_request`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `request`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/payment_service.rs","byte_start":9599,"byte_end":9606,"line_start":275,"line_end":275,"column_start":58,"column_end":65,"is_primary":true,"text":[{"text":"    async fn create_alipay_payment(&self, order: &Order, request: &PaymentRequest) -> AppResult<PaymentResponse> {","highlight_start":58,"highlight_end":65}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/payment_service.rs","byte_start":9599,"byte_end":9606,"line_start":275,"line_end":275,"column_start":58,"column_end":65,"is_primary":true,"text":[{"text":"    async fn create_alipay_payment(&self, order: &Order, request: &PaymentRequest) -> AppResult<PaymentResponse> {","highlight_start":58,"highlight_end":65}],"label":null,"suggested_replacement":"_request","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `request`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/payment_service.rs:275:58\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m275\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn create_alipay_payment(&self, order: &Order, request: &PaymentRequest) -> AppResult<PaymentResponse> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_request`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `request`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/payment_service.rs","byte_start":10942,"byte_end":10949,"line_start":302,"line_end":302,"column_start":59,"column_end":66,"is_primary":true,"text":[{"text":"    async fn create_balance_payment(&self, order: &Order, request: &PaymentRequest) -> AppResult<PaymentResponse> {","highlight_start":59,"highlight_end":66}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/payment_service.rs","byte_start":10942,"byte_end":10949,"line_start":302,"line_end":302,"column_start":59,"column_end":66,"is_primary":true,"text":[{"text":"    async fn create_balance_payment(&self, order: &Order, request: &PaymentRequest) -> AppResult<PaymentResponse> {","highlight_start":59,"highlight_end":66}],"label":null,"suggested_replacement":"_request","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `request`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/payment_service.rs:302:59\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m302\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn create_balance_payment(&self, order: &Order, request: &PaymentRequest) -> AppResult<PaymentResponse> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_request`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `referrer`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/commission_service.rs","byte_start":3674,"byte_end":3682,"line_start":133,"line_end":133,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"        let referrer = self.user_service.find_by_id(referrer_id).await?","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/commission_service.rs","byte_start":3674,"byte_end":3682,"line_start":133,"line_end":133,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"        let referrer = self.user_service.find_by_id(referrer_id).await?","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":"_referrer","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `referrer`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/commission_service.rs:133:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m133\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let referrer = self.user_service.find_by_id(referrer_id).await?\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_referrer`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `referee`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/commission_service.rs","byte_start":3850,"byte_end":3857,"line_start":136,"line_end":136,"column_start":13,"column_end":20,"is_primary":true,"text":[{"text":"        let referee = self.user_service.find_by_id(referee_id).await?","highlight_start":13,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/commission_service.rs","byte_start":3850,"byte_end":3857,"line_start":136,"line_end":136,"column_start":13,"column_end":20,"is_primary":true,"text":[{"text":"        let referee = self.user_service.find_by_id(referee_id).await?","highlight_start":13,"highlight_end":20}],"label":null,"suggested_replacement":"_referee","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `referee`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/commission_service.rs:136:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m136\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let referee = self.user_service.find_by_id(referee_id).await?\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_referee`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `user`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/commission_service.rs","byte_start":7350,"byte_end":7354,"line_start":234,"line_end":234,"column_start":13,"column_end":17,"is_primary":true,"text":[{"text":"        let user = self.user_service.find_by_id(request.user_id).await?","highlight_start":13,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/commission_service.rs","byte_start":7350,"byte_end":7354,"line_start":234,"line_end":234,"column_start":13,"column_end":17,"is_primary":true,"text":[{"text":"        let user = self.user_service.find_by_id(request.user_id).await?","highlight_start":13,"highlight_end":17}],"label":null,"suggested_replacement":"_user","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `user`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/commission_service.rs:234:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m234\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let user = self.user_service.find_by_id(request.user_id).await?\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_user`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `user_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/commission_service.rs","byte_start":8128,"byte_end":8135,"line_start":252,"line_end":252,"column_start":45,"column_end":52,"is_primary":true,"text":[{"text":"    async fn get_user_referral_chain(&self, user_id: i32) -> AppResult<Vec<ReferralRelation>> {","highlight_start":45,"highlight_end":52}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/commission_service.rs","byte_start":8128,"byte_end":8135,"line_start":252,"line_end":252,"column_start":45,"column_end":52,"is_primary":true,"text":[{"text":"    async fn get_user_referral_chain(&self, user_id: i32) -> AppResult<Vec<ReferralRelation>> {","highlight_start":45,"highlight_end":52}],"label":null,"suggested_replacement":"_user_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `user_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/commission_service.rs:252:45\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m252\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_user_referral_chain(&self, user_id: i32) -> AppResult<Vec<ReferralRelation>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_user_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `order_sn`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/commission_service.rs","byte_start":8840,"byte_end":8848,"line_start":276,"line_end":276,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"        order_sn: &str,","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/commission_service.rs","byte_start":8840,"byte_end":8848,"line_start":276,"line_end":276,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"        order_sn: &str,","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":"_order_sn","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `order_sn`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/commission_service.rs:276:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m276\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        order_sn: &str,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_order_sn`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `commission_rate`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/commission_service.rs","byte_start":8896,"byte_end":8911,"line_start":278,"line_end":278,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"        commission_rate: f64,","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/commission_service.rs","byte_start":8896,"byte_end":8911,"line_start":278,"line_end":278,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"        commission_rate: f64,","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":"_commission_rate","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `commission_rate`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/commission_service.rs:278:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m278\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        commission_rate: f64,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_commission_rate`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `referrer_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/commission_service.rs","byte_start":9326,"byte_end":9337,"line_start":292,"line_end":292,"column_start":52,"column_end":63,"is_primary":true,"text":[{"text":"    async fn check_referral_relation_exists(&self, referrer_id: i32, referee_id: i32) -> AppResult<bool> {","highlight_start":52,"highlight_end":63}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/commission_service.rs","byte_start":9326,"byte_end":9337,"line_start":292,"line_end":292,"column_start":52,"column_end":63,"is_primary":true,"text":[{"text":"    async fn check_referral_relation_exists(&self, referrer_id: i32, referee_id: i32) -> AppResult<bool> {","highlight_start":52,"highlight_end":63}],"label":null,"suggested_replacement":"_referrer_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `referrer_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/commission_service.rs:292:52\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m292\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn check_referral_relation_exists(&self, referrer_id: i32, referee_id: i32) -> AppResult<bool> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_referrer_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `referee_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/commission_service.rs","byte_start":9344,"byte_end":9354,"line_start":292,"line_end":292,"column_start":70,"column_end":80,"is_primary":true,"text":[{"text":"    async fn check_referral_relation_exists(&self, referrer_id: i32, referee_id: i32) -> AppResult<bool> {","highlight_start":70,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/commission_service.rs","byte_start":9344,"byte_end":9354,"line_start":292,"line_end":292,"column_start":70,"column_end":80,"is_primary":true,"text":[{"text":"    async fn check_referral_relation_exists(&self, referrer_id: i32, referee_id: i32) -> AppResult<bool> {","highlight_start":70,"highlight_end":80}],"label":null,"suggested_replacement":"_referee_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `referee_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/commission_service.rs:292:70\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m292\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn check_referral_relation_exists(&self, referrer_id: i32, referee_id: i32) -> AppResult<bool> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_referee_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `referrer_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/commission_service.rs","byte_start":9532,"byte_end":9543,"line_start":298,"line_end":298,"column_start":45,"column_end":56,"is_primary":true,"text":[{"text":"    async fn check_circular_referral(&self, referrer_id: i32, referee_id: i32) -> AppResult<bool> {","highlight_start":45,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/commission_service.rs","byte_start":9532,"byte_end":9543,"line_start":298,"line_end":298,"column_start":45,"column_end":56,"is_primary":true,"text":[{"text":"    async fn check_circular_referral(&self, referrer_id: i32, referee_id: i32) -> AppResult<bool> {","highlight_start":45,"highlight_end":56}],"label":null,"suggested_replacement":"_referrer_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `referrer_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/commission_service.rs:298:45\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m298\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn check_circular_referral(&self, referrer_id: i32, referee_id: i32) -> AppResult<bool> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_referrer_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `referee_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/commission_service.rs","byte_start":9550,"byte_end":9560,"line_start":298,"line_end":298,"column_start":63,"column_end":73,"is_primary":true,"text":[{"text":"    async fn check_circular_referral(&self, referrer_id: i32, referee_id: i32) -> AppResult<bool> {","highlight_start":63,"highlight_end":73}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/commission_service.rs","byte_start":9550,"byte_end":9560,"line_start":298,"line_end":298,"column_start":63,"column_end":73,"is_primary":true,"text":[{"text":"    async fn check_circular_referral(&self, referrer_id: i32, referee_id: i32) -> AppResult<bool> {","highlight_start":63,"highlight_end":73}],"label":null,"suggested_replacement":"_referee_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `referee_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/commission_service.rs:298:63\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m298\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn check_circular_referral(&self, referrer_id: i32, referee_id: i32) -> AppResult<bool> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_referee_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `referrer_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/commission_service.rs","byte_start":9727,"byte_end":9738,"line_start":304,"line_end":304,"column_start":46,"column_end":57,"is_primary":true,"text":[{"text":"    async fn insert_referral_relation(&self, referrer_id: i32, referee_id: i32, level: i8) -> AppResult<()> {","highlight_start":46,"highlight_end":57}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/commission_service.rs","byte_start":9727,"byte_end":9738,"line_start":304,"line_end":304,"column_start":46,"column_end":57,"is_primary":true,"text":[{"text":"    async fn insert_referral_relation(&self, referrer_id: i32, referee_id: i32, level: i8) -> AppResult<()> {","highlight_start":46,"highlight_end":57}],"label":null,"suggested_replacement":"_referrer_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `referrer_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/commission_service.rs:304:46\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m304\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn insert_referral_relation(&self, referrer_id: i32, referee_id: i32, level: i8) -> AppResult<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_referrer_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `referee_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/commission_service.rs","byte_start":9745,"byte_end":9755,"line_start":304,"line_end":304,"column_start":64,"column_end":74,"is_primary":true,"text":[{"text":"    async fn insert_referral_relation(&self, referrer_id: i32, referee_id: i32, level: i8) -> AppResult<()> {","highlight_start":64,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/commission_service.rs","byte_start":9745,"byte_end":9755,"line_start":304,"line_end":304,"column_start":64,"column_end":74,"is_primary":true,"text":[{"text":"    async fn insert_referral_relation(&self, referrer_id: i32, referee_id: i32, level: i8) -> AppResult<()> {","highlight_start":64,"highlight_end":74}],"label":null,"suggested_replacement":"_referee_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `referee_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/commission_service.rs:304:64\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m304\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn insert_referral_relation(&self, referrer_id: i32, referee_id: i32, level: i8) -> AppResult<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_referee_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `level`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/commission_service.rs","byte_start":9762,"byte_end":9767,"line_start":304,"line_end":304,"column_start":81,"column_end":86,"is_primary":true,"text":[{"text":"    async fn insert_referral_relation(&self, referrer_id: i32, referee_id: i32, level: i8) -> AppResult<()> {","highlight_start":81,"highlight_end":86}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/commission_service.rs","byte_start":9762,"byte_end":9767,"line_start":304,"line_end":304,"column_start":81,"column_end":86,"is_primary":true,"text":[{"text":"    async fn insert_referral_relation(&self, referrer_id: i32, referee_id: i32, level: i8) -> AppResult<()> {","highlight_start":81,"highlight_end":86}],"label":null,"suggested_replacement":"_level","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `level`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/commission_service.rs:304:81\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m304\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn insert_referral_relation(&self, referrer_id: i32, referee_id: i32, level: i8) -> AppResult<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_level`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `user_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/commission_service.rs","byte_start":9940,"byte_end":9947,"line_start":310,"line_end":310,"column_start":49,"column_end":56,"is_primary":true,"text":[{"text":"    async fn get_user_pending_commission(&self, user_id: i32) -> AppResult<f64> {","highlight_start":49,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/commission_service.rs","byte_start":9940,"byte_end":9947,"line_start":310,"line_end":310,"column_start":49,"column_end":56,"is_primary":true,"text":[{"text":"    async fn get_user_pending_commission(&self, user_id: i32) -> AppResult<f64> {","highlight_start":49,"highlight_end":56}],"label":null,"suggested_replacement":"_user_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `user_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/commission_service.rs:310:49\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m310\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_user_pending_commission(&self, user_id: i32) -> AppResult<f64> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_user_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"method `get_rate_limiter` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/middleware/rate_limit.rs","byte_start":13317,"byte_end":13345,"line_start":434,"line_end":434,"column_start":1,"column_end":29,"is_primary":false,"text":[{"text":"impl PathRateLimitMiddleware {","highlight_start":1,"highlight_end":29}],"label":"method in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/middleware/rate_limit.rs","byte_start":13923,"byte_end":13939,"line_start":455,"line_end":455,"column_start":8,"column_end":24,"is_primary":true,"text":[{"text":"    fn get_rate_limiter(&self, path: &str) -> Option<&RateLimiter> {","highlight_start":8,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: method `get_rate_limiter` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/middleware/rate_limit.rs:455:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m434\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl PathRateLimitMiddleware {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethod in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m455\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn get_rate_limiter(&self, path: &str) -> Option<&RateLimiter> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `order_repo` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/services/payment_service.rs","byte_start":2982,"byte_end":2996,"line_start":118,"line_end":118,"column_start":12,"column_end":26,"is_primary":false,"text":[{"text":"pub struct PaymentService {","highlight_start":12,"highlight_end":26}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/payment_service.rs","byte_start":3003,"byte_end":3013,"line_start":119,"line_end":119,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    order_repo: OrderRepository,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: field `order_repo` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/payment_service.rs:119:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m118\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct PaymentService {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m119\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    order_repo: OrderRepository,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `order_repo` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/services/commission_service.rs","byte_start":2871,"byte_end":2888,"line_start":107,"line_end":107,"column_start":12,"column_end":29,"is_primary":false,"text":[{"text":"pub struct CommissionService {","highlight_start":12,"highlight_end":29}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/commission_service.rs","byte_start":2926,"byte_end":2936,"line_start":109,"line_end":109,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    order_repo: OrderRepository,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: field `order_repo` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/commission_service.rs:109:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m107\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct CommissionService {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m108\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    user_repo: UserRepository,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m109\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    order_repo: OrderRepository,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `sign_name` and `client` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/services/external_service.rs","byte_start":474,"byte_end":484,"line_start":21,"line_end":21,"column_start":12,"column_end":22,"is_primary":false,"text":[{"text":"pub struct SmsService {","highlight_start":12,"highlight_end":22}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/external_service.rs","byte_start":539,"byte_end":548,"line_start":24,"line_end":24,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"    sign_name: String,","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/external_service.rs","byte_start":562,"byte_end":568,"line_start":25,"line_end":25,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    client: Client,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: fields `sign_name` and `client` are never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/external_service.rs:24:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct SmsService {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    sign_name: String,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    client: Client,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `secret_key` and `client` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/services/external_service.rs","byte_start":615,"byte_end":625,"line_start":29,"line_end":29,"column_start":12,"column_end":22,"is_primary":false,"text":[{"text":"pub struct OssService {","highlight_start":12,"highlight_end":22}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/external_service.rs","byte_start":678,"byte_end":688,"line_start":32,"line_end":32,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    secret_key: String,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/external_service.rs","byte_start":722,"byte_end":728,"line_start":34,"line_end":34,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    client: Client,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: fields `secret_key` and `client` are never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/external_service.rs:32:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m29\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct OssService {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m32\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    secret_key: String,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    bucket: String,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m34\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    client: Client,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"29 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 29 warnings emitted\u001b[0m\n\n"}
