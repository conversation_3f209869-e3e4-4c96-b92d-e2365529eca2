{"rustc": 15497389221046826682, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"bigdecimal\", \"chrono\", \"default\", \"json\", \"migrate\", \"mysql\", \"postgres\", \"sqlx-mysql\", \"sqlx-postgres\", \"tokio\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tokio\", \"uuid\"]", "target": 961973412475639632, "profile": 3033921117576893, "path": 16916974361567732181, "deps": [[530211389790465181, "hex", false, 10586778938960923610], [996810380461694889, "sqlx_core", false, 14753550785662482108], [1441306149310335789, "tempfile", false, 4423097694943249554], [2713742371683562785, "syn", false, 2912462015904633979], [3060637413840920116, "proc_macro2", false, 4073356016091061424], [3150220818285335163, "url", false, 1022189923837994226], [3405707034081185165, "dotenvy", false, 10161143526320191517], [3722963349756955755, "once_cell", false, 4661176198988501949], [8045585743974080694, "heck", false, 3278093969477493491], [9538054652646069845, "tokio", false, 9970771616705131479], [9689903380558560274, "serde", false, 15600985978075100854], [9857275760291862238, "sha2", false, 11460053878085018430], [12170264697963848012, "either", false, 14398140745955925487], [15367738274754116744, "serde_json", false, 9585828743781793823], [15634168271133386882, "sqlx_postgres", false, 3813434790782564020], [15948984357385107951, "sqlx_mysql", false, 13794229394194211026], [17990358020177143287, "quote", false, 13515053211124113931]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-macros-core-be33a1cb55ba55aa/dep-lib-sqlx_macros_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}