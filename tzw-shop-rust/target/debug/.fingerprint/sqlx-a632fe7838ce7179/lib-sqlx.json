{"rustc": 15497389221046826682, "features": "[\"_rt-tokio\", \"any\", \"bigdecimal\", \"chrono\", \"default\", \"json\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"runtime-tokio\", \"runtime-tokio-rustls\", \"sqlx-macros\", \"sqlx-mysql\", \"sqlx-postgres\", \"tls-rustls\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_unstable-all-types\", \"all-databases\", \"any\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"regexp\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"rust_decimal\", \"sqlite\", \"sqlx-macros\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tls-native-tls\", \"tls-none\", \"tls-rustls\", \"uuid\"]", "target": 3003836824758849296, "profile": 8276155916380437441, "path": 9430412274986020881, "deps": [[228475551920078470, "sqlx_macros", false, 411912617616063775], [996810380461694889, "sqlx_core", false, 5875570529577801572], [15634168271133386882, "sqlx_postgres", false, 14147654654364506206], [15948984357385107951, "sqlx_mysql", false, 17378569266102905060]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-a632fe7838ce7179/dep-lib-sqlx", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}