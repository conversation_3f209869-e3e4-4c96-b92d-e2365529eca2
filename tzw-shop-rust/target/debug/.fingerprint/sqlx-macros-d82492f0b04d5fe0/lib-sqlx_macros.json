{"rustc": 15497389221046826682, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"chrono\", \"default\", \"json\", \"migrate\", \"mysql\", \"rust_decimal\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"time\", \"uuid\"]", "target": 13494433325021527976, "profile": 3033921117576893, "path": 9341506807393246492, "deps": [[996810380461694889, "sqlx_core", false, 2410507291317270357], [2713742371683562785, "syn", false, 2912462015904633979], [3060637413840920116, "proc_macro2", false, 4073356016091061424], [15733334431800349573, "sqlx_macros_core", false, 15484526588555235570], [17990358020177143287, "quote", false, 13515053211124113931]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-macros-d82492f0b04d5fe0/dep-lib-sqlx_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}