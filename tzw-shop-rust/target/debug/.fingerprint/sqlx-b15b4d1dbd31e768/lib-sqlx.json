{"rustc": 15497389221046826682, "features": "[\"_rt-tokio\", \"any\", \"chrono\", \"default\", \"json\", \"macros\", \"migrate\", \"mysql\", \"runtime-tokio\", \"runtime-tokio-rustls\", \"rust_decimal\", \"sqlx-macros\", \"sqlx-mysql\", \"tls-rustls\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_unstable-all-types\", \"all-databases\", \"any\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"regexp\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"rust_decimal\", \"sqlite\", \"sqlx-macros\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tls-native-tls\", \"tls-none\", \"tls-rustls\", \"uuid\"]", "target": 3003836824758849296, "profile": 8276155916380437441, "path": 9430412274986020881, "deps": [[228475551920078470, "sqlx_macros", false, 6022159900358717230], [996810380461694889, "sqlx_core", false, 11831754104335352994], [15948984357385107951, "sqlx_mysql", false, 3053752902420367257]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-b15b4d1dbd31e768/dep-lib-sqlx", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}