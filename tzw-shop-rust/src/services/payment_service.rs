//! 支付集成服务
//!
//! 集成微信支付、支付宝支付，实现支付回调处理和订单状态同步

use rust_decimal::Decimal;
use crate::error::{AppError, AppResult};
use crate::models::order::Order;
use crate::models::common::OrderStatus;
use crate::repositories::order_repo::OrderRepository;
use crate::services::{BaseService, OrderService};
use async_trait::async_trait;
use chrono::Utc;
use serde::{Deserialize, Serialize};
use sqlx::{MySql, Pool};
use std::collections::HashMap;
use std::sync::Arc;
use tracing::{info, instrument};
use validator::Validate;

/// 支付方式枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum PaymentMethod {
    /// 微信支付
    Wechat,
    /// 支付宝
    Alipay,
    /// 余额支付
    Balance,
}

/// 支付请求
#[derive(Debug, Serialize, Deserialize, validator::Validate)]
pub struct PaymentRequest {
    /// 订单ID
    pub order_id: i32,
    /// 支付方式
    pub payment_method: PaymentMethod,
    /// 支付金额（分）
    #[validate(range(min = 1, message = "支付金额必须大于0"))]
    pub amount: i64,
    /// 用户IP
    pub client_ip: Option<String>,
    /// 回调地址
    pub notify_url: Option<String>,
    /// 返回地址
    pub return_url: Option<String>,
}

/// 支付响应
#[derive(Debug, Serialize)]
pub struct PaymentResponse {
    /// 支付方式
    pub payment_method: PaymentMethod,
    /// 预支付ID
    pub prepay_id: String,
    /// 支付参数（用于前端调起支付）
    pub payment_params: HashMap<String, String>,
    /// 支付链接（H5支付）
    pub payment_url: Option<String>,
}

/// 退款请求
#[derive(Debug, Serialize, Deserialize, validator::Validate)]
pub struct RefundRequest {
    /// 订单ID
    pub order_id: i32,
    /// 退款金额（分）
    #[validate(range(min = 1, message = "退款金额必须大于0"))]
    pub refund_amount: i64,
    /// 退款原因
    #[validate(length(min = 1, max = 255, message = "退款原因长度1-255字符"))]
    pub reason: String,
    /// 退款单号
    pub refund_sn: Option<String>,
}

/// 退款响应
#[derive(Debug, Serialize)]
pub struct RefundResponse {
    /// 退款单号
    pub refund_sn: String,
    /// 退款状态
    pub status: String,
    /// 退款金额
    pub refund_amount: i64,
}

/// 支付配置
#[derive(Debug, Clone)]
pub struct PaymentConfig {
    /// 微信支付配置
    pub wechat: WechatPayConfig,
    /// 支付宝配置
    pub alipay: AlipayConfig,
}

/// 微信支付配置
#[derive(Debug, Clone)]
pub struct WechatPayConfig {
    pub app_id: String,
    pub mch_id: String,
    pub api_key: String,
    pub cert_path: Option<String>,
    pub key_path: Option<String>,
    pub notify_url: String,
}

/// 支付宝配置
#[derive(Debug, Clone)]
pub struct AlipayConfig {
    pub app_id: String,
    pub private_key: String,
    pub public_key: String,
    pub notify_url: String,
    pub return_url: String,
}

/// 支付服务
pub struct PaymentService {
    order_repo: OrderRepository,
    order_service: Arc<OrderService>,
    config: PaymentConfig,
}

impl PaymentService {
    /// 创建支付服务
    pub fn new(
        db_pool: Arc<Pool<MySql>>,
        order_service: Arc<OrderService>,
    ) -> Self {
        let config = PaymentConfig {
            wechat: WechatPayConfig {
                app_id: std::env::var("WECHAT_APP_ID").unwrap_or_default(),
                mch_id: std::env::var("WECHAT_MCH_ID").unwrap_or_default(),
                api_key: std::env::var("WECHAT_API_KEY").unwrap_or_default(),
                cert_path: std::env::var("WECHAT_CERT_PATH").ok(),
                key_path: std::env::var("WECHAT_KEY_PATH").ok(),
                notify_url: std::env::var("WECHAT_NOTIFY_URL").unwrap_or_default(),
            },
            alipay: AlipayConfig {
                app_id: std::env::var("ALIPAY_APP_ID").unwrap_or_default(),
                private_key: std::env::var("ALIPAY_PRIVATE_KEY").unwrap_or_default(),
                public_key: std::env::var("ALIPAY_PUBLIC_KEY").unwrap_or_default(),
                notify_url: std::env::var("ALIPAY_NOTIFY_URL").unwrap_or_default(),
                return_url: std::env::var("ALIPAY_RETURN_URL").unwrap_or_default(),
            },
        };

        Self {
            order_repo: OrderRepository::new(db_pool.as_ref().clone()),
            order_service,
            config,
        }
    }

    /// 创建支付订单
    #[instrument(skip(self, request))]
    pub async fn create_payment(&self, request: PaymentRequest) -> AppResult<PaymentResponse> {
        // 验证输入
        request.validate().map_err(|e| AppError::ValidationError(e.to_string()))?;

        // 获取订单信息
        let order = self.order_service.find_by_id(request.order_id).await?
            .ok_or_else(|| AppError::NotFound { resource: format!("Order#{}", request.order_id) })?;

        // 检查订单状态
        if !order.order_status.can_pay() {
            return Err(AppError::BusinessError("订单状态不允许支付".to_string()));
        }

        // 检查支付金额
        let order_amount = (&order.actual_price * Decimal::from(100))
            .to_i64()
            .ok_or_else(|| AppError::BusinessError("订单金额转换失败".to_string()))?; // 转换为分
        if request.amount != order_amount {
            return Err(AppError::BusinessError("支付金额与订单金额不符".to_string()));
        }

        // 根据支付方式创建支付订单
        match request.payment_method {
            PaymentMethod::Wechat => self.create_wechat_payment(&order, &request).await,
            PaymentMethod::Alipay => self.create_alipay_payment(&order, &request).await,
            PaymentMethod::Balance => self.create_balance_payment(&order, &request).await,
        }
    }

    /// 处理支付回调
    #[instrument(skip(self, notify_data))]
    pub async fn handle_payment_notify(&self, payment_method: PaymentMethod, notify_data: &str) -> AppResult<String> {
        match payment_method {
            PaymentMethod::Wechat => self.handle_wechat_notify(notify_data).await,
            PaymentMethod::Alipay => self.handle_alipay_notify(notify_data).await,
            PaymentMethod::Balance => {
                // 余额支付不需要回调处理
                Ok("SUCCESS".to_string())
            }
        }
    }

    /// 查询支付状态
    #[instrument(skip(self))]
    pub async fn query_payment_status(&self, order_id: i32) -> AppResult<PaymentStatus> {
        let order = self.order_service.find_by_id(order_id).await?
            .ok_or_else(|| AppError::NotFound { resource: format!("Order#{}", order_id) })?;

        let status = match order.order_status {
            OrderStatus::Unpaid => PaymentStatus::Pending,
            OrderStatus::Paid => PaymentStatus::Success,
            OrderStatus::Cancelled => PaymentStatus::Cancelled,
            _ => PaymentStatus::Success,
        };

        Ok(status)
    }

    /// 申请退款
    #[instrument(skip(self, request))]
    pub async fn create_refund(&self, request: RefundRequest) -> AppResult<RefundResponse> {
        // 验证输入
        request.validate().map_err(|e| AppError::ValidationError(e.to_string()))?;

        // 获取订单信息
        let order = self.order_service.find_by_id(request.order_id).await?
            .ok_or_else(|| AppError::NotFound { resource: format!("Order#{}", request.order_id) })?;

        // 检查订单状态
        if !matches!(order.order_status, OrderStatus::Paid | OrderStatus::Shipped | OrderStatus::Received) {
            return Err(AppError::BusinessError("订单状态不允许退款".to_string()));
        }

        // 检查退款金额
        let order_amount = (&order.actual_price * Decimal::from(100))
            .to_i64()
            .ok_or_else(|| AppError::BusinessError("订单金额转换失败".to_string()))?;
        if request.refund_amount > order_amount {
            return Err(AppError::BusinessError("退款金额不能超过订单金额".to_string()));
        }

        // 生成退款单号
        let refund_sn = request.refund_sn.unwrap_or_else(|| self.generate_refund_sn());

        // TODO: 调用第三方支付接口申请退款
        // 这里暂时返回模拟数据
        info!(order_id = request.order_id, refund_amount = request.refund_amount, "退款申请成功");

        Ok(RefundResponse {
            refund_sn,
            status: "PROCESSING".to_string(),
            refund_amount: request.refund_amount,
        })
    }

    /// 创建微信支付订单
    async fn create_wechat_payment(&self, order: &Order, request: &PaymentRequest) -> AppResult<PaymentResponse> {
        // TODO: 实现微信支付统一下单接口
        // 这里返回模拟数据
        let prepay_id = format!("wx_prepay_{}", order.id);
        
        let mut payment_params = HashMap::new();
        payment_params.insert("appId".to_string(), self.config.wechat.app_id.clone());
        payment_params.insert("timeStamp".to_string(), Utc::now().timestamp().to_string());
        payment_params.insert("nonceStr".to_string(), self.generate_nonce_str());
        payment_params.insert("package".to_string(), format!("prepay_id={}", prepay_id));
        payment_params.insert("signType".to_string(), "MD5".to_string());
        
        // TODO: 计算签名
        payment_params.insert("paySign".to_string(), "mock_sign".to_string());

        info!(order_id = order.id, prepay_id = %prepay_id, "微信支付订单创建成功");

        Ok(PaymentResponse {
            payment_method: PaymentMethod::Wechat,
            prepay_id,
            payment_params,
            payment_url: None,
        })
    }

    /// 创建支付宝支付订单
    async fn create_alipay_payment(&self, order: &Order, request: &PaymentRequest) -> AppResult<PaymentResponse> {
        // TODO: 实现支付宝统一下单接口
        // 这里返回模拟数据
        let prepay_id = format!("alipay_prepay_{}", order.id);
        
        let mut payment_params = HashMap::new();
        payment_params.insert("app_id".to_string(), self.config.alipay.app_id.clone());
        payment_params.insert("method".to_string(), "alipay.trade.app.pay".to_string());
        payment_params.insert("charset".to_string(), "utf-8".to_string());
        payment_params.insert("sign_type".to_string(), "RSA2".to_string());
        payment_params.insert("timestamp".to_string(), Utc::now().format("%Y-%m-%d %H:%M:%S").to_string());
        payment_params.insert("version".to_string(), "1.0".to_string());
        
        // TODO: 计算签名
        payment_params.insert("sign".to_string(), "mock_sign".to_string());

        info!(order_id = order.id, prepay_id = %prepay_id, "支付宝支付订单创建成功");

        Ok(PaymentResponse {
            payment_method: PaymentMethod::Alipay,
            prepay_id: prepay_id.clone(),
            payment_params,
            payment_url: Some(format!("https://openapi.alipay.com/gateway.do?{}", prepay_id)),
        })
    }

    /// 创建余额支付订单
    async fn create_balance_payment(&self, order: &Order, request: &PaymentRequest) -> AppResult<PaymentResponse> {
        // TODO: 检查用户余额是否足够
        // TODO: 扣减用户余额
        // TODO: 更新订单状态为已支付
        
        let prepay_id = format!("balance_pay_{}", order.id);
        
        // 直接标记为支付成功
        self.order_service.pay_order(order.id, &prepay_id).await?;

        info!(order_id = order.id, "余额支付成功");

        Ok(PaymentResponse {
            payment_method: PaymentMethod::Balance,
            prepay_id,
            payment_params: HashMap::new(),
            payment_url: None,
        })
    }

    /// 处理微信支付回调
    async fn handle_wechat_notify(&self, notify_data: &str) -> AppResult<String> {
        // TODO: 验证微信支付回调签名
        // TODO: 解析回调数据
        // TODO: 更新订单状态
        
        info!(notify_data = %notify_data, "收到微信支付回调");
        
        // 模拟处理成功
        Ok("<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>".to_string())
    }

    /// 处理支付宝回调
    async fn handle_alipay_notify(&self, notify_data: &str) -> AppResult<String> {
        // TODO: 验证支付宝回调签名
        // TODO: 解析回调数据
        // TODO: 更新订单状态
        
        info!(notify_data = %notify_data, "收到支付宝回调");
        
        // 模拟处理成功
        Ok("success".to_string())
    }

    /// 生成随机字符串
    fn generate_nonce_str(&self) -> String {
        use rand::Rng;
        let mut rng = rand::thread_rng();
        (0..32).map(|_| {
            let idx = rng.gen_range(0..62);
            match idx {
                0..=25 => (b'a' + idx) as char,
                26..=51 => (b'A' + (idx - 26)) as char,
                _ => (b'0' + (idx - 52)) as char,
            }
        }).collect()
    }

    /// 生成退款单号
    fn generate_refund_sn(&self) -> String {
        let timestamp = Utc::now().timestamp();
        let random = rand::random::<u32>() % 10000;
        format!("RF{}{:04}", timestamp, random)
    }
}

/// 支付状态枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum PaymentStatus {
    /// 待支付
    Pending,
    /// 支付成功
    Success,
    /// 支付失败
    Failed,
    /// 已取消
    Cancelled,
}

#[async_trait]
impl BaseService for PaymentService {
    fn service_name(&self) -> &'static str {
        "PaymentService"
    }

    async fn health_check(&self) -> AppResult<bool> {
        // 检查配置是否完整
        if self.config.wechat.app_id.is_empty() || self.config.alipay.app_id.is_empty() {
            return Ok(false);
        }
        
        // 检查依赖服务
        self.order_service.health_check().await
    }
}
