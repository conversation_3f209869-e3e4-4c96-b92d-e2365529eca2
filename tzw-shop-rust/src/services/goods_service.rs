//! 商品管理服务
//! 
//! 提供商品CRUD、库存管理、分类管理、搜索功能和推荐算法

use crate::error::{AppError, AppResult};
use crate::models::goods::{Goods, GoodsQuery, SearchQuery, GoodsDetail};
use crate::models::common::{PageQuery, PageResult, ApproveStatus};
use crate::repositories::goods_repo::GoodsRepository;
use crate::repositories::base_repo::Repository;
use crate::services::{BaseService, CacheService, PageParams, PageResponse};
use async_trait::async_trait;
use chrono::Utc;
use rust_decimal::Decimal;
use sqlx::{MySql, Pool};
use std::sync::Arc;
use tracing::{info, instrument};
use validator::Validate;

/// 商品创建请求
#[derive(Debug, serde::Deserialize, validator::Validate)]
pub struct CreateGoodsRequest {
    #[validate(length(min = 1, max = 120, message = "商品名称长度1-120字符"))]
    pub name: String,
    #[validate(length(min = 1, max = 63, message = "商品编号长度1-63字符"))]
    pub goods_sn: String,
    pub category_id: Option<i32>,
    pub brand_id: Option<i32>,
    pub gallery: Option<Vec<String>>,
    pub keywords: Option<String>,
    pub brief: Option<String>,
    pub is_on_sale: Option<bool>,
    pub sort_order: Option<i32>,
    pub pic_url: Option<String>,
    pub share_url: Option<String>,
    pub is_new: Option<bool>,
    pub is_hot: Option<bool>,
    pub unit: Option<String>,
    pub counter_price: Option<Decimal>,
    #[validate(range(min = 0.0, message = "零售价格不能为负数"))]
    pub retail_price: Option<Decimal>,
    pub detail: Option<String>,
    pub wholesale_price: Option<Decimal>,
    pub brokerage_price: Option<Decimal>,
}

/// 商品更新请求
#[derive(Debug, serde::Deserialize, validator::Validate)]
pub struct UpdateGoodsRequest {
    pub id: i32,
    #[validate(length(min = 1, max = 120, message = "商品名称长度1-120字符"))]
    pub name: Option<String>,
    pub category_id: Option<i32>,
    pub brand_id: Option<i32>,
    pub gallery: Option<Vec<String>>,
    pub keywords: Option<String>,
    pub brief: Option<String>,
    pub is_on_sale: Option<bool>,
    pub sort_order: Option<i32>,
    pub pic_url: Option<String>,
    pub share_url: Option<String>,
    pub is_new: Option<bool>,
    pub is_hot: Option<bool>,
    pub unit: Option<String>,
    pub counter_price: Option<Decimal>,
    pub retail_price: Option<Decimal>,
    pub detail: Option<String>,
    pub wholesale_price: Option<Decimal>,
    pub brokerage_price: Option<Decimal>,
}

/// 商品搜索请求
#[derive(Debug, serde::Deserialize)]
pub struct GoodsSearchRequest {
    #[serde(flatten)]
    pub page: PageParams,
    pub keyword: Option<String>,
    pub category_id: Option<i32>,
    pub brand_id: Option<i32>,
    pub min_price: Option<Decimal>,
    pub max_price: Option<Decimal>,
    pub is_on_sale: Option<bool>,
    pub is_new: Option<bool>,
    pub is_hot: Option<bool>,
    pub sort_by: Option<String>, // price, sales, add_time
    pub sort_order: Option<String>, // asc, desc
}

/// 商品推荐类型
#[derive(Debug, Clone)]
pub enum RecommendType {
    /// 热门商品
    Hot,
    /// 新品推荐
    New,
    /// 相关商品
    Related(i32), // 商品ID
    /// 用户推荐
    UserBased(i32), // 用户ID
}

/// 商品服务
pub struct GoodsService {
    goods_repo: GoodsRepository,
    cache_service: Arc<CacheService>,
}

impl GoodsService {
    /// 创建商品服务
    pub fn new(
        db_pool: Arc<Pool<MySql>>,
        cache_service: Arc<CacheService>,
    ) -> Self {
        Self {
            goods_repo: GoodsRepository::new(db_pool.as_ref().clone()),
            cache_service,
        }
    }

    /// 根据ID查找商品
    #[instrument(skip(self))]
    pub async fn find_by_id(&self, goods_id: i32) -> AppResult<Option<Goods>> {
        // 先从缓存查找
        let cache_key = format!("goods:id:{}", goods_id);
        if let Some(goods) = self.cache_service.get_json::<Goods>(&cache_key).await? {
            return Ok(Some(goods));
        }

        // 从数据库查找
        let goods = self.goods_repo.find_by_id(goods_id).await?;
        
        // 缓存商品信息（10分钟）
        if let Some(ref goods) = goods {
            self.cache_service.set_json(&cache_key, goods, Some(600)).await?;
        }

        Ok(goods)
    }

    /// 获取商品详情（包含规格、属性等）
    #[instrument(skip(self))]
    pub async fn get_goods_detail(&self, goods_id: i32) -> AppResult<Option<GoodsDetail>> {
        // 先从缓存查找
        let cache_key = format!("goods:detail:{}", goods_id);
        if let Some(detail) = self.cache_service.get_json::<GoodsDetail>(&cache_key).await? {
            return Ok(Some(detail));
        }

        // 从数据库查找
        let detail = self.goods_repo.find_detail_by_id(goods_id).await?;
        
        // 缓存商品详情（10分钟）
        if let Some(ref detail) = detail {
            self.cache_service.set_json(&cache_key, detail, Some(600)).await?;
        }

        Ok(detail)
    }

    /// 创建商品
    #[instrument(skip(self, request))]
    pub async fn create_goods(&self, request: CreateGoodsRequest) -> AppResult<i32> {
        // 验证输入
        request.validate().map_err(|e| AppError::ValidationError(e.to_string()))?;

        // 检查商品编号是否重复
        if self.goods_repo.exists_by_goods_sn(&request.goods_sn).await? {
            return Err(AppError::BusinessError("商品编号已存在".to_string()));
        }

        // 创建商品对象
        let goods = Goods {
            id: 0, // 将由数据库生成
            goods_sn: request.goods_sn,
            name: request.name,
            category_id: request.category_id,
            brand_id: request.brand_id,
            gallery: request.gallery,
            keywords: request.keywords,
            brief: request.brief,
            is_on_sale: request.is_on_sale,
            sort_order: request.sort_order.map(|x| x as i16),
            pic_url: request.pic_url,
            share_url: request.share_url,
            is_new: request.is_new,
            is_hot: request.is_hot,
            unit: request.unit,
            counter_price: request.counter_price,
            retail_price: request.retail_price,
            detail: request.detail,
            add_time: Some(Utc::now()),
            update_time: Some(Utc::now()),
            browse: 0,
            sales: 0,
            deleted: false,
            commpany: None,
            wholesale_price: request.wholesale_price,
            approve_status: ApproveStatus::Pending,
            approve_msg: None,
            brokerage_type: crate::models::common::BrokerageType::None,
            brokerage_price: request.brokerage_price,
        };

        let goods_id = self.goods_repo.create(&goods).await?;
        
        // 清除相关缓存
        self.clear_goods_cache().await?;
        
        info!(goods_id = goods_id, name = %goods.name, "商品创建成功");
        Ok(goods_id)
    }

    /// 更新商品
    #[instrument(skip(self, request))]
    pub async fn update_goods(&self, request: UpdateGoodsRequest) -> AppResult<()> {
        // 验证输入
        request.validate().map_err(|e| AppError::ValidationError(e.to_string()))?;

        // 获取现有商品
        let mut goods = self.find_by_id(request.id).await?
            .ok_or_else(|| AppError::NotFound { resource: format!("Goods#{}", request.id) })?;

        // 更新字段
        if let Some(name) = request.name {
            goods.name = name;
        }
        if let Some(category_id) = request.category_id {
            goods.category_id = Some(category_id);
        }
        if let Some(brand_id) = request.brand_id {
            goods.brand_id = Some(brand_id);
        }
        if let Some(gallery) = request.gallery {
            goods.gallery = Some(gallery);
        }
        if let Some(keywords) = request.keywords {
            goods.keywords = Some(keywords);
        }
        if let Some(brief) = request.brief {
            goods.brief = Some(brief);
        }
        if let Some(is_on_sale) = request.is_on_sale {
            goods.is_on_sale = Some(is_on_sale);
        }
        if let Some(sort_order) = request.sort_order {
            goods.sort_order = Some(sort_order as i16);
        }
        if let Some(pic_url) = request.pic_url {
            goods.pic_url = Some(pic_url);
        }
        if let Some(share_url) = request.share_url {
            goods.share_url = Some(share_url);
        }
        if let Some(is_new) = request.is_new {
            goods.is_new = Some(is_new);
        }
        if let Some(is_hot) = request.is_hot {
            goods.is_hot = Some(is_hot);
        }
        if let Some(unit) = request.unit {
            goods.unit = Some(unit);
        }
        if let Some(counter_price) = request.counter_price {
            goods.counter_price = Some(counter_price);
        }
        if let Some(retail_price) = request.retail_price {
            goods.retail_price = Some(retail_price);
        }
        if let Some(detail) = request.detail {
            goods.detail = Some(detail);
        }
        if let Some(wholesale_price) = request.wholesale_price {
            goods.wholesale_price = Some(wholesale_price);
        }
        if let Some(brokerage_price) = request.brokerage_price {
            goods.brokerage_price = Some(brokerage_price);
        }

        goods.update_time = Some(Utc::now());

        // 更新数据库
        self.goods_repo.update(&goods).await?;
        
        // 清除相关缓存
        self.clear_goods_cache_by_id(request.id).await?;
        
        info!(goods_id = request.id, "商品更新成功");
        Ok(())
    }

    /// 删除商品（逻辑删除）
    #[instrument(skip(self))]
    pub async fn delete_goods(&self, goods_id: i32) -> AppResult<()> {
        self.goods_repo.delete(goods_id).await?;
        
        // 清除相关缓存
        self.clear_goods_cache_by_id(goods_id).await?;
        
        info!(goods_id = goods_id, "商品删除成功");
        Ok(())
    }

    /// 搜索商品
    #[instrument(skip(self))]
    pub async fn search_goods(&self, request: GoodsSearchRequest) -> AppResult<PageResponse<Goods>> {
        let mut page_params = request.page;
        page_params.validate();

        // 构建搜索查询
        let search_query = SearchQuery {
            page: PageQuery {
                page: page_params.page,
                limit: page_params.limit,
            },
            keyword: request.keyword,
            category_id: request.category_id,
            min_price: request.min_price,
            max_price: request.max_price,
            sort_by: request.sort_by,
            sort_order: request.sort_order,
            user_id: None, // 搜索历史记录用
        };

        let page_result = self.goods_repo.search(&search_query).await?;

        Ok(PageResponse::new(
            page_result.list,
            page_result.total,
            page_params.page,
            page_params.limit,
        ))
    }

    /// 分页查询商品
    #[instrument(skip(self))]
    pub async fn find_goods(&self, params: PageParams, query: Option<GoodsQuery>) -> AppResult<PageResponse<Goods>> {
        let mut page_params = params;
        page_params.validate();

        let page_query = PageQuery {
            page: page_params.page,
            limit: page_params.limit,
        };

        let page_result = if let Some(query) = query {
            self.goods_repo.find_by_query(&query).await?
        } else {
            self.goods_repo.find_page(&page_query).await?
        };

        Ok(PageResponse::new(
            page_result.list,
            page_result.total,
            page_params.page,
            page_params.limit,
        ))
    }

    /// 获取商品推荐
    #[instrument(skip(self))]
    pub async fn get_recommendations(&self, recommend_type: RecommendType, limit: u32) -> AppResult<Vec<Goods>> {
        // 先从缓存查找
        let cache_key = match &recommend_type {
            RecommendType::Hot => "goods:recommend:hot".to_string(),
            RecommendType::New => "goods:recommend:new".to_string(),
            RecommendType::Related(goods_id) => format!("goods:recommend:related:{}", goods_id),
            RecommendType::UserBased(user_id) => format!("goods:recommend:user:{}", user_id),
        };

        if let Some(goods_list) = self.cache_service.get_json::<Vec<Goods>>(&cache_key).await? {
            return Ok(goods_list.into_iter().take(limit as usize).collect());
        }

        // 从数据库获取推荐
        let goods_list = match recommend_type {
            RecommendType::Hot => {
                self.goods_repo.find_hot_goods(limit).await?
            }
            RecommendType::New => {
                self.goods_repo.find_new_goods(limit).await?
            }
            RecommendType::Related(goods_id) => {
                self.goods_repo.find_related_goods(goods_id, limit).await?
            }
            RecommendType::UserBased(_user_id) => {
                // TODO: 实现基于用户的推荐算法
                self.goods_repo.find_hot_goods(limit).await?
            }
        };

        // 缓存推荐结果（30分钟）
        self.cache_service.set_json(&cache_key, &goods_list, Some(1800)).await?;

        Ok(goods_list)
    }

    /// 增加商品浏览量
    #[instrument(skip(self))]
    pub async fn increment_browse(&self, goods_id: i32) -> AppResult<()> {
        self.goods_repo.increment_browse(goods_id).await?;
        
        // 清除商品缓存
        self.clear_goods_cache_by_id(goods_id).await?;
        
        Ok(())
    }

    /// 增加商品销量
    #[instrument(skip(self))]
    pub async fn increment_sales(&self, goods_id: i32, quantity: i32) -> AppResult<()> {
        self.goods_repo.increment_sales(goods_id, quantity).await?;
        
        // 清除商品缓存
        self.clear_goods_cache_by_id(goods_id).await?;
        
        Ok(())
    }

    /// 更新商品状态
    #[instrument(skip(self))]
    pub async fn update_goods_status(&self, goods_id: i32, is_on_sale: bool) -> AppResult<()> {
        self.goods_repo.update_on_sale_status(goods_id, is_on_sale).await?;
        
        // 清除商品缓存
        self.clear_goods_cache_by_id(goods_id).await?;
        
        info!(goods_id = goods_id, is_on_sale = is_on_sale, "商品状态更新成功");
        Ok(())
    }

    /// 审核商品
    #[instrument(skip(self))]
    pub async fn approve_goods(&self, goods_id: i32, status: ApproveStatus, message: Option<String>) -> AppResult<()> {
        self.goods_repo.update_approve_status(goods_id, status, message.as_deref()).await?;
        
        // 清除商品缓存
        self.clear_goods_cache_by_id(goods_id).await?;
        
        info!(goods_id = goods_id, status = ?status, "商品审核完成");
        Ok(())
    }

    /// 获取商品统计信息
    #[instrument(skip(self))]
    pub async fn get_goods_stats(&self) -> AppResult<GoodsStats> {
        // 先从缓存获取
        let cache_key = "goods:stats";
        if let Some(stats) = self.cache_service.get_json::<GoodsStats>(cache_key).await? {
            return Ok(stats);
        }

        // 从数据库统计
        let total_goods = self.goods_repo.count_goods().await?;
        let on_sale_goods = self.goods_repo.count_on_sale_goods().await?;
        let new_goods_today = self.goods_repo.count_new_goods_today().await?;

        let stats = GoodsStats {
            total_goods,
            on_sale_goods,
            new_goods_today,
        };

        // 缓存统计信息（10分钟）
        self.cache_service.set_json(cache_key, &stats, Some(600)).await?;

        Ok(stats)
    }

    /// 清除商品相关缓存
    async fn clear_goods_cache(&self) -> AppResult<()> {
        // 清除推荐缓存
        self.cache_service.delete_pattern("goods:recommend:*").await?;
        // 清除统计缓存
        self.cache_service.delete("goods:stats").await?;
        Ok(())
    }

    /// 清除指定商品的缓存
    async fn clear_goods_cache_by_id(&self, goods_id: i32) -> AppResult<()> {
        let keys = vec![
            format!("goods:id:{}", goods_id),
            format!("goods:detail:{}", goods_id),
        ];

        for key in keys {
            self.cache_service.delete(&key).await?;
        }

        // 清除推荐缓存
        self.clear_goods_cache().await?;

        Ok(())
    }
}

/// 商品统计信息
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct GoodsStats {
    /// 商品总数
    pub total_goods: u64,
    /// 上架商品数
    pub on_sale_goods: u64,
    /// 今日新增商品数
    pub new_goods_today: u64,
}

#[async_trait]
impl BaseService for GoodsService {
    fn service_name(&self) -> &'static str {
        "GoodsService"
    }

    async fn health_check(&self) -> AppResult<bool> {
        // 检查数据库连接
        match self.goods_repo.count_goods().await {
            Ok(_) => Ok(true),
            Err(_) => Ok(false),
        }
    }
}
