//! 订单处理服务
//!
//! 提供订单创建、状态流转、支付处理、发货管理和退款处理

use rust_decimal::Decimal;
use crate::error::{AppError, AppResult};
use crate::models::order::{Order, OrderGoods, OrderQuery};
use crate::models::common::{PageQuery, PageResult, OrderStatus, FreightType};
use crate::repositories::order_repo::OrderRepository;
use crate::repositories::base_repo::Repository;
use crate::services::{BaseService, GoodsService, UserService, PageParams, PageResponse};
use async_trait::async_trait;
use chrono::Utc;
use sqlx::{MySql, Pool};
use std::sync::Arc;
use tracing::{info, warn, instrument};
use validator::Validate;

/// 订单创建请求
#[derive(Debug, serde::Deserialize, validator::Validate)]
pub struct CreateOrderRequest {
    pub user_id: i32,
    #[validate(length(min = 1, max = 63, message = "收货人姓名长度1-63字符"))]
    pub consignee: String,
    #[validate(length(min = 11, max = 11, message = "手机号格式不正确"))]
    pub mobile: String,
    #[validate(length(min = 1, max = 127, message = "收货地址长度1-127字符"))]
    pub address: String,
    pub message: Option<String>,
    #[validate(length(min = 1, message = "订单商品不能为空"))]
    pub goods_list: Vec<OrderGoodsItem>,
    pub freight_type: Option<FreightType>,
    pub coupon_id: Option<i32>,
    pub use_integral: Option<i32>,
}

/// 订单商品项
#[derive(Debug, serde::Serialize, serde::Deserialize, validator::Validate)]
pub struct OrderGoodsItem {
    pub goods_id: i32,
    pub product_id: i32,
    #[validate(range(min = 1, message = "商品数量至少为1"))]
    pub number: i32,
    pub price: Option<Decimal>, // 如果不提供，从商品信息获取
}

/// 订单更新请求
#[derive(Debug, serde::Deserialize)]
pub struct UpdateOrderRequest {
    pub order_id: i32,
    pub consignee: Option<String>,
    pub mobile: Option<String>,
    pub address: Option<String>,
    pub message: Option<String>,
}

/// 发货请求
#[derive(Debug, serde::Deserialize, validator::Validate)]
pub struct ShipOrderRequest {
    pub order_id: i32,
    #[validate(length(min = 1, message = "快递单号不能为空"))]
    pub ship_sn: String,
    #[validate(length(min = 1, message = "快递公司不能为空"))]
    pub ship_channel: String,
}

/// 退款请求
#[derive(Debug, serde::Deserialize, validator::Validate)]
pub struct RefundOrderRequest {
    pub order_id: i32,
    #[validate(length(min = 1, message = "退款原因不能为空"))]
    pub reason: String,
    pub refund_amount: Option<Decimal>, // 如果不提供，退全款
}

/// 订单搜索请求
#[derive(Debug, serde::Deserialize)]
pub struct OrderSearchRequest {
    #[serde(flatten)]
    pub page: PageParams,
    pub user_id: Option<i32>,
    pub order_sn: Option<String>,
    pub status: Option<OrderStatus>,
    pub start_time: Option<chrono::DateTime<Utc>>,
    pub end_time: Option<chrono::DateTime<Utc>>,
    pub consignee: Option<String>,
    pub mobile: Option<String>,
}

/// 订单详情响应
#[derive(Debug, serde::Serialize)]
pub struct OrderDetailResponse {
    pub order: Order,
    pub goods_list: Vec<OrderGoods>,
    pub user_info: Option<UserInfo>,
}

/// 用户信息（简化版）
#[derive(Debug, serde::Serialize)]
pub struct UserInfo {
    pub id: i32,
    pub username: String,
    pub nickname: String,
    pub mobile: String,
}

/// 订单服务
pub struct OrderService {
    order_repo: OrderRepository,
    goods_service: Arc<GoodsService>,
    user_service: Arc<UserService>,
}

impl OrderService {
    /// 创建订单服务
    pub fn new(
        db_pool: Arc<Pool<MySql>>,
        goods_service: Arc<GoodsService>,
        user_service: Arc<UserService>,
    ) -> Self {
        Self {
            order_repo: OrderRepository::new(db_pool.as_ref().clone()),
            goods_service,
            user_service,
        }
    }

    /// 根据ID查找订单
    #[instrument(skip(self))]
    pub async fn find_by_id(&self, order_id: i32) -> AppResult<Option<Order>> {
        self.order_repo.find_by_id(order_id).await
    }

    /// 根据订单号查找订单
    #[instrument(skip(self))]
    pub async fn find_by_order_sn(&self, order_sn: &str) -> AppResult<Option<Order>> {
        self.order_repo.find_by_order_sn(order_sn).await
    }

    /// 获取订单详情
    #[instrument(skip(self))]
    pub async fn get_order_detail(&self, order_id: i32) -> AppResult<Option<OrderDetailResponse>> {
        // 获取订单信息
        let order = match self.find_by_id(order_id).await? {
            Some(order) => order,
            None => return Ok(None),
        };

        // 获取订单商品列表
        let goods_list = self.order_repo.find_order_goods(order_id).await?;

        // 获取用户信息
        let user_info = if let Some(user) = self.user_service.find_by_id(order.user_id).await? {
            Some(UserInfo {
                id: user.id,
                username: user.username,
                nickname: user.nickname,
                mobile: user.mobile,
            })
        } else {
            None
        };

        Ok(Some(OrderDetailResponse {
            order,
            goods_list,
            user_info,
        }))
    }

    /// 创建订单
    #[instrument(skip(self, request))]
    pub async fn create_order(&self, request: CreateOrderRequest) -> AppResult<i32> {
        // 验证输入
        request.validate().map_err(|e| AppError::ValidationError(e.to_string()))?;

        // 验证用户是否存在
        let user = self.user_service.find_by_id(request.user_id).await?
            .ok_or_else(|| AppError::NotFound { resource: format!("User#{}", request.user_id) })?;

        // 验证商品信息并计算价格
        let mut goods_price = Decimal::from(0);
        let mut order_goods_list = Vec::new();

        for goods_item in &request.goods_list {
            // 获取商品信息
            let goods = self.goods_service.find_by_id(goods_item.goods_id).await?
                .ok_or_else(|| AppError::NotFound { resource: format!("Goods#{}", goods_item.goods_id) })?;

            // 检查商品是否上架
            if goods.is_on_sale != Some(true) {
                return Err(AppError::BusinessError(format!("商品 {} 已下架", goods.name)));
            }

            // 获取商品价格
            let price = goods_item.price.as_ref()
                .cloned()
                .or_else(|| goods.retail_price.clone())
                .unwrap_or_else(|| Decimal::from(0));
            goods_price += &price * Decimal::from(goods_item.number);

            // 构建订单商品
            order_goods_list.push(OrderGoods {
                id: 0,
                order_id: 0, // 稍后设置
                brand_id: goods.brand_id,
                goods_id: goods_item.goods_id,
                goods_name: goods.name.clone(),
                goods_sn: goods.goods_sn.clone(),
                product_id: goods_item.product_id,
                number: goods_item.number as i16,
                price: price.clone(),
                specifications: vec![], // 空的规格列表
                pic_url: goods.pic_url.clone().unwrap_or_default(),
                comment: None,
                add_time: Some(Utc::now()),
                update_time: Some(Utc::now()),
                deleted: false,
                refund_id: None,
                settlement_money: None,
            });
        }

        // 计算运费
        let freight_price = self.calculate_freight(&request.address, &goods_price).await?;

        // 计算优惠券减免
        let coupon_price = if let Some(coupon_id) = request.coupon_id {
            self.calculate_coupon_discount(coupon_id, &goods_price).await?
        } else {
            Decimal::from(0)
        };

        // 计算积分减免
        let integral_price = if let Some(use_integral) = request.use_integral {
            self.calculate_integral_discount(request.user_id, use_integral).await?
        } else {
            Decimal::from(0)
        };

        // 计算订单总价
        let order_price = &goods_price + &freight_price - &coupon_price;
        let actual_price = &order_price - &integral_price;

        if actual_price < Decimal::from(0) {
            return Err(AppError::BusinessError("订单金额不能为负数".to_string()));
        }

        // 生成订单号
        let order_sn = self.generate_order_sn().await?;

        // 创建订单
        let order = Order {
            id: 0,
            user_id: request.user_id,
            order_sn,
            order_status: OrderStatus::Unpaid,
            consignee: request.consignee,
            mobile: request.mobile,
            address: request.address,
            message: request.message.unwrap_or_default(),
            goods_price: goods_price.clone(),
            freight_price: Some(freight_price.clone()),
            coupon_price: coupon_price.clone(),
            integral_price: integral_price.clone(),
            groupon_price: Decimal::from(0),
            order_price: order_price.clone(),
            actual_price: actual_price.clone(),
            pay_id: None,
            pay_time: None,
            ship_sn: None,
            ship_channel: None,
            ship_time: None,
            confirm_time: None,
            comments: Some(0),
            end_time: None,
            add_time: Some(Utc::now()),
            update_time: Some(Utc::now()),
            deleted: false,
            settlement_money: None,
            settlement_status: false,
            freight_type: request.freight_type.unwrap_or(FreightType::Express),
            share_user_id: None,
            fetch_code: None,
            create_user_id: None,
            gift_send_time: None,
            gift_receive_time: None,
        };

        // 保存订单
        let order_id = self.order_repo.create(&order).await?;

        // 保存订单商品
        for mut order_goods in order_goods_list {
            order_goods.order_id = order_id;
            self.order_repo.create_order_goods(&order_goods).await?;
        }

        info!(order_id = order_id, user_id = request.user_id, actual_price = actual_price, "订单创建成功");
        Ok(order_id)
    }

    /// 更新订单信息
    #[instrument(skip(self, request))]
    pub async fn update_order(&self, request: UpdateOrderRequest) -> AppResult<()> {
        // 获取现有订单
        let mut order = self.find_by_id(request.order_id).await?
            .ok_or_else(|| AppError::NotFound { resource: format!("Order#{}", request.order_id) })?;

        // 检查订单状态是否允许修改
        if !matches!(order.order_status, OrderStatus::Unpaid) {
            return Err(AppError::BusinessError("只有未支付订单才能修改".to_string()));
        }

        // 更新字段
        if let Some(consignee) = request.consignee {
            order.consignee = consignee;
        }
        if let Some(mobile) = request.mobile {
            order.mobile = mobile;
        }
        if let Some(address) = request.address {
            order.address = address;
        }
        if let Some(message) = request.message {
            order.message = message;
        }

        order.update_time = Some(Utc::now());

        // 更新数据库
        self.order_repo.update(&order).await?;

        info!(order_id = request.order_id, "订单更新成功");
        Ok(())
    }

    /// 取消订单
    #[instrument(skip(self))]
    pub async fn cancel_order(&self, order_id: i32, reason: Option<String>) -> AppResult<()> {
        let order = self.find_by_id(order_id).await?
            .ok_or_else(|| AppError::NotFound { resource: format!("Order#{}", order_id) })?;

        // 检查订单状态
        if !order.order_status.can_cancel() {
            return Err(AppError::BusinessError("当前订单状态不允许取消".to_string()));
        }

        // 更新订单状态
        self.order_repo.update_status(order_id, OrderStatus::Cancelled).await?;

        // 如果已支付，需要退款
        if matches!(order.order_status, OrderStatus::Paid | OrderStatus::Shipped) {
            // TODO: 调用退款接口
            warn!(order_id = order_id, "订单取消，需要处理退款");
        }

        info!(order_id = order_id, reason = ?reason, "订单取消成功");
        Ok(())
    }

    /// 支付订单
    #[instrument(skip(self))]
    pub async fn pay_order(&self, order_id: i32, pay_id: &str) -> AppResult<()> {
        let order = self.find_by_id(order_id).await?
            .ok_or_else(|| AppError::NotFound { resource: format!("Order#{}", order_id) })?;

        // 检查订单状态
        if !order.order_status.can_pay() {
            return Err(AppError::BusinessError("当前订单状态不允许支付".to_string()));
        }

        // 更新订单状态和支付信息
        self.order_repo.update_payment_info(order_id, pay_id).await?;
        self.order_repo.update_status(order_id, OrderStatus::Paid).await?;

        // 增加商品销量
        let goods_list = self.order_repo.find_order_goods(order_id).await?;
        for goods in goods_list {
            self.goods_service.increment_sales(goods.goods_id, goods.number.into()).await?;
        }

        info!(order_id = order_id, pay_id = %pay_id, "订单支付成功");
        Ok(())
    }

    /// 发货
    #[instrument(skip(self, request))]
    pub async fn ship_order(&self, request: ShipOrderRequest) -> AppResult<()> {
        // 验证输入
        request.validate().map_err(|e| AppError::ValidationError(e.to_string()))?;

        let order = self.find_by_id(request.order_id).await?
            .ok_or_else(|| AppError::NotFound { resource: format!("Order#{}", request.order_id) })?;

        // 检查订单状态
        if !order.order_status.can_ship() {
            return Err(AppError::BusinessError("当前订单状态不允许发货".to_string()));
        }

        // 更新发货信息
        self.order_repo.update_shipping_info(
            request.order_id,
            &request.ship_sn,
            &request.ship_channel,
        ).await?;
        self.order_repo.update_status(request.order_id, OrderStatus::Shipped).await?;

        info!(order_id = request.order_id, ship_sn = %request.ship_sn, "订单发货成功");
        Ok(())
    }

    /// 确认收货
    #[instrument(skip(self))]
    pub async fn confirm_order(&self, order_id: i32) -> AppResult<()> {
        let order = self.find_by_id(order_id).await?
            .ok_or_else(|| AppError::NotFound { resource: format!("Order#{}", order_id) })?;

        // 检查订单状态
        if !order.order_status.can_confirm() {
            return Err(AppError::BusinessError("当前订单状态不允许确认收货".to_string()));
        }

        // 更新订单状态
        self.order_repo.update_confirm_time(order_id, &Utc::now()).await?;
        self.order_repo.update_status(order_id, OrderStatus::Received).await?;

        info!(order_id = order_id, "订单确认收货成功");
        Ok(())
    }

    /// 搜索订单
    #[instrument(skip(self))]
    pub async fn search_orders(&self, request: OrderSearchRequest) -> AppResult<PageResponse<Order>> {
        let mut page_params = request.page;
        page_params.validate();

        // 构建查询条件
        let query = OrderQuery {
            page: PageQuery {
                page: page_params.page,
                limit: page_params.limit,
            },
            user_id: request.user_id,
            order_sn: request.order_sn,
            order_status: request.status,
            consignee: request.consignee,
            mobile: request.mobile,
            freight_type: None,
            start_time: request.start_time,
            end_time: request.end_time,
        };

        let page_result = self.order_repo.find_by_query(&query).await?;

        Ok(PageResponse::new(
            page_result.list,
            page_result.total,
            page_params.page,
            page_params.limit,
        ))
    }

    /// 获取订单统计信息
    #[instrument(skip(self))]
    pub async fn get_order_stats(&self) -> AppResult<OrderStats> {
        let total_orders = self.order_repo.count_orders().await?;
        let paid_orders = self.order_repo.count_orders_by_status(OrderStatus::Paid).await?;
        let shipped_orders = self.order_repo.count_orders_by_status(OrderStatus::Shipped).await?;
        let total_amount = self.order_repo.calculate_total_amount().await?;

        Ok(OrderStats {
            total_orders,
            paid_orders,
            shipped_orders,
            total_amount,
        })
    }

    /// 生成订单号
    async fn generate_order_sn(&self) -> AppResult<String> {
        let now = Utc::now();
        let timestamp = now.timestamp();
        let random = rand::random::<u32>() % 10000;
        Ok(format!("{}{:04}", timestamp, random))
    }

    /// 计算运费
    async fn calculate_freight(&self, _address: &str, goods_price: &Decimal) -> AppResult<Decimal> {
        // TODO: 实现运费计算逻辑
        // 这里简单实现：满99免运费，否则10元运费
        if goods_price >= &Decimal::from(99) {
            Ok(Decimal::from(0))
        } else {
            Ok(Decimal::from(10))
        }
    }

    /// 计算优惠券减免
    async fn calculate_coupon_discount(&self, _coupon_id: i32, _goods_price: &Decimal) -> AppResult<Decimal> {
        // TODO: 实现优惠券计算逻辑
        Ok(Decimal::from(0))
    }

    /// 计算积分减免
    async fn calculate_integral_discount(&self, _user_id: i32, _use_integral: i32) -> AppResult<Decimal> {
        // TODO: 实现积分减免计算逻辑
        Ok(Decimal::from(0))
    }
}

/// 订单统计信息
#[derive(Debug, Clone, serde::Serialize)]
pub struct OrderStats {
    /// 订单总数
    pub total_orders: u64,
    /// 已支付订单数
    pub paid_orders: u64,
    /// 已发货订单数
    pub shipped_orders: u64,
    /// 总交易金额
    pub total_amount: Decimal,
}

#[async_trait]
impl BaseService for OrderService {
    fn service_name(&self) -> &'static str {
        "OrderService"
    }

    async fn health_check(&self) -> AppResult<bool> {
        // 检查数据库连接
        match self.order_repo.count_orders().await {
            Ok(_) => Ok(true),
            Err(_) => Ok(false),
        }
    }
}
