//! 数据访问层模块
//!
//! 提供数据库访问抽象

pub mod user_repo;
pub mod order_repo;
pub mod goods_repo;
pub mod base_repo;

// 重新导出仓储
pub use user_repo::UserRepository;
pub use order_repo::OrderRepository;
pub use goods_repo::GoodsRepository;
pub use base_repo::*;

#[cfg(test)]
mod tests {
    use super::*;
    use rust_decimal::Decimal;
    use crate::models::common::{PageQuery, UserStatus, UserLevel, Gender, OrderStatus};
    use crate::models::user::User;
    use crate::models::goods::Goods;
    use crate::models::order::Order;
    use chrono::Utc;
    use sqlx::{MySql, Pool};

    /// 创建测试数据库连接池
    async fn create_test_pool() -> Pool<MySql> {
        // 注意：这里需要配置测试数据库连接
        // 在实际测试中，应该使用测试专用的数据库
        let database_url = std::env::var("TEST_DATABASE_URL")
            .unwrap_or_else(|_| "mysql://root:password@localhost/dts_shop_test".to_string());

        sqlx::MySqlPool::connect(&database_url)
            .await
            .expect("Failed to connect to test database")
    }

    /// 创建测试用户
    fn create_test_user(id: i32) -> User {
        User {
            id,
            username: format!("test_user_{}", id),
            password: "hashed_password".to_string(),
            gender: Gender::Male,
            birthday: None,
            last_login_time: None,
            last_login_ip: "127.0.0.1".to_string(),
            user_level: UserLevel::Normal,
            nickname: format!("Test User {}", id),
            mobile: format!("1380000{:04}", id),
            avatar: "".to_string(),
            weixin_openid: "".to_string(),
            status: UserStatus::Active,
            add_time: Some(Utc::now()),
            update_time: Some(Utc::now()),
            deleted: false,
            share_user_id: None,
        }
    }

    /// 创建测试商品
    fn create_test_goods(id: i32) -> Goods {
        Goods {
            id,
            goods_sn: format!("G{:08}", id),
            name: format!("Test Goods {}", id),
            category_id: Some(1),
            brand_id: Some(1),
            gallery: Some(vec!["image1.jpg".to_string(), "image2.jpg".to_string()]),
            keywords: Some("test,goods".to_string()),
            brief: Some("Test goods description".to_string()),
            is_on_sale: Some(true),
            sort_order: Some(100),
            pic_url: Some("main.jpg".to_string()),
            share_url: Some("share.jpg".to_string()),
            is_new: Some(false),
            is_hot: Some(false),
            unit: Some("件".to_string()),
            counter_price: Some(Decimal::from(19999) / Decimal::from(100)), // 199.99
            retail_price: Some(Decimal::from(9999) / Decimal::from(100)), // 99.99
            detail: Some("<p>Detailed description</p>".to_string()),
            add_time: Some(Utc::now()),
            update_time: Some(Utc::now()),
            browse: 0,
            sales: 0,
            deleted: false,
            commpany: Some("Test Company".to_string()),
            wholesale_price: Some(Decimal::from(7999) / Decimal::from(100)), // 79.99
            approve_status: crate::models::common::ApproveStatus::Approved,
            approve_msg: None,
            brokerage_type: crate::models::common::BrokerageType::None,
            brokerage_price: None,
        }
    }

    /// 创建测试订单
    fn create_test_order(id: i32, user_id: i32) -> Order {
        Order {
            id,
            user_id,
            order_sn: format!("O{:014}", id),
            order_status: OrderStatus::Unpaid,
            consignee: "Test User".to_string(),
            mobile: "13800000000".to_string(),
            address: "Test Address".to_string(),
            message: "".to_string(),
            goods_price: Decimal::from(9999) / Decimal::from(100), // 99.99
            freight_price: Some(Decimal::from(10)),
            coupon_price: Decimal::from(0),
            integral_price: Decimal::from(0),
            groupon_price: Decimal::from(0),
            order_price: Decimal::from(10999) / Decimal::from(100), // 109.99
            actual_price: Decimal::from(10999) / Decimal::from(100), // 109.99
            pay_id: None,
            pay_time: None,
            ship_sn: None,
            ship_channel: None,
            ship_time: None,
            confirm_time: None,
            comments: Some(0),
            end_time: None,
            add_time: Some(Utc::now()),
            update_time: Some(Utc::now()),
            deleted: false,
            settlement_money: None,
            settlement_status: false,
            freight_type: crate::models::common::FreightType::Express,
            share_user_id: None,
            fetch_code: None,
            create_user_id: None,
            gift_send_time: None,
            gift_receive_time: None,
        }
    }

    #[tokio::test]
    #[ignore] // 需要数据库连接，在CI中忽略
    async fn test_user_repository_crud() {
        let pool = create_test_pool().await;
        let repo = UserRepository::new(pool);

        // 测试创建用户
        let test_user = create_test_user(999);
        let user_id = repo.create(&test_user).await.expect("Failed to create user");
        assert!(user_id > 0);

        // 测试查找用户
        let found_user = repo.find_by_id(user_id).await.expect("Failed to find user");
        assert!(found_user.is_some());
        let found_user = found_user.unwrap();
        assert_eq!(found_user.username, test_user.username);

        // 测试按用户名查找
        let found_by_username = repo.find_by_username(&test_user.username).await
            .expect("Failed to find user by username");
        assert!(found_by_username.is_some());

        // 测试按手机号查找
        let found_by_mobile = repo.find_by_mobile(&test_user.mobile).await
            .expect("Failed to find user by mobile");
        assert!(found_by_mobile.is_some());

        // 测试用户名是否存在
        let username_exists = repo.username_exists(&test_user.username, None).await
            .expect("Failed to check username existence");
        assert!(username_exists);

        // 测试手机号是否存在
        let mobile_exists = repo.mobile_exists(&test_user.mobile, None).await
            .expect("Failed to check mobile existence");
        assert!(mobile_exists);

        // 测试分页查询
        let page_query = PageQuery { page: 1, limit: 10 };
        let page_result = repo.find_page(&page_query).await
            .expect("Failed to find page");
        assert!(page_result.total > 0);

        // 测试删除用户
        repo.delete(user_id).await.expect("Failed to delete user");

        // 验证用户已被逻辑删除
        let deleted_user = repo.find_by_id(user_id).await.expect("Failed to find deleted user");
        assert!(deleted_user.is_none());
    }

    #[tokio::test]
    #[ignore] // 需要数据库连接，在CI中忽略
    async fn test_goods_repository_crud() {
        let pool = create_test_pool().await;
        let repo = GoodsRepository::new(pool);

        // 测试创建商品
        let test_goods = create_test_goods(999);
        let goods_id = repo.create(&test_goods).await.expect("Failed to create goods");
        assert!(goods_id > 0);

        // 测试查找商品
        let found_goods = repo.find_by_id(goods_id).await.expect("Failed to find goods");
        assert!(found_goods.is_some());
        let found_goods = found_goods.unwrap();
        assert_eq!(found_goods.name, test_goods.name);

        // 测试增加浏览量
        repo.increment_browse(goods_id).await.expect("Failed to increment browse");

        // 测试增加销量
        repo.increment_sales(goods_id, 5).await.expect("Failed to increment sales");

        // 测试分页查询
        let page_query = PageQuery { page: 1, limit: 10 };
        let page_result = repo.find_page(&page_query).await
            .expect("Failed to find page");
        assert!(page_result.total > 0);

        // 测试删除商品
        repo.delete(goods_id).await.expect("Failed to delete goods");

        // 验证商品已被逻辑删除
        let deleted_goods = repo.find_by_id(goods_id).await.expect("Failed to find deleted goods");
        assert!(deleted_goods.is_none());
    }

    #[tokio::test]
    #[ignore] // 需要数据库连接，在CI中忽略
    async fn test_order_repository_crud() {
        let pool = create_test_pool().await;
        let repo = OrderRepository::new(pool);

        // 测试创建订单
        let test_order = create_test_order(999, 1);
        let order_id = repo.create(&test_order).await.expect("Failed to create order");
        assert!(order_id > 0);

        // 测试查找订单
        let found_order = repo.find_by_id(order_id).await.expect("Failed to find order");
        assert!(found_order.is_some());
        let found_order = found_order.unwrap();
        assert_eq!(found_order.order_sn, test_order.order_sn);

        // 测试按订单号查找
        let found_by_sn = repo.find_by_order_sn(&test_order.order_sn).await
            .expect("Failed to find order by sn");
        assert!(found_by_sn.is_some());

        // 测试更新订单状态
        repo.update_status(order_id, OrderStatus::Paid).await
            .expect("Failed to update order status");

        // 验证状态已更新
        let updated_order = repo.find_by_id(order_id).await
            .expect("Failed to find updated order").unwrap();
        assert_eq!(updated_order.order_status, OrderStatus::Paid);

        // 测试分页查询
        let page_query = PageQuery { page: 1, limit: 10 };
        let page_result = repo.find_page(&page_query).await
            .expect("Failed to find page");
        assert!(page_result.total > 0);

        // 测试删除订单
        repo.delete(order_id).await.expect("Failed to delete order");

        // 验证订单已被逻辑删除
        let deleted_order = repo.find_by_id(order_id).await.expect("Failed to find deleted order");
        assert!(deleted_order.is_none());
    }

    #[test]
    fn test_query_builder() {
        // 测试基础查询构建
        let builder = QueryBuilder::new("test_table");
        let sql = builder
            .select(&["id", "name", "status"])
            .where_clause("status = 1")
            .order_by("id", "DESC")
            .limit(10)
            .offset(20)
            .build();

        assert!(sql.contains("SELECT id, name, status"));
        assert!(sql.contains("FROM test_table"));
        assert!(sql.contains("WHERE deleted = 0 AND status = 1"));
        assert!(sql.contains("ORDER BY id DESC"));
        assert!(sql.contains("LIMIT 10"));
        assert!(sql.contains("OFFSET 20"));

        // 测试计数查询构建
        let builder2 = QueryBuilder::new("test_table");
        let count_sql = builder2.build_count();
        assert!(count_sql.contains("SELECT COUNT(*) as count"));
        assert!(count_sql.contains("FROM test_table"));
    }

    #[test]
    fn test_page_query() {
        let mut page = PageQuery { page: 0, limit: 0 };
        page.validate();
        assert_eq!(page.page, 1);
        assert_eq!(page.limit, 20);

        let page = PageQuery { page: 2, limit: 50 };
        assert_eq!(page.offset(), 50);

        let mut page = PageQuery { page: 1, limit: 200 };
        page.validate();
        assert_eq!(page.limit, 20); // 超过最大限制，重置为默认值
    }

    #[test]
    fn test_order_status_transitions() {
        let status = OrderStatus::Unpaid;
        assert!(status.can_pay());
        assert!(status.can_cancel());
        assert!(!status.can_ship());
        assert!(!status.can_confirm());
        assert!(!status.can_comment());

        let status = OrderStatus::Paid;
        assert!(!status.can_pay());
        assert!(!status.can_cancel());
        assert!(status.can_ship());
        assert!(!status.can_confirm());
        assert!(!status.can_comment());

        let status = OrderStatus::Shipped;
        assert!(!status.can_pay());
        assert!(!status.can_cancel());
        assert!(!status.can_ship());
        assert!(status.can_confirm());
        assert!(!status.can_comment());

        let status = OrderStatus::Received;
        assert!(!status.can_pay());
        assert!(!status.can_cancel());
        assert!(!status.can_ship());
        assert!(!status.can_confirm());
        assert!(status.can_comment());
    }

    #[test]
    fn test_order_price_calculation() {
        let order = create_test_order(1, 1);

        // 测试订单总价计算
        let calculated_order_price = order.calculate_order_price();
        let expected = &order.goods_price + order.freight_price.as_ref().unwrap_or(&Decimal::from(0)) - &order.coupon_price - &order.groupon_price;
        assert_eq!(calculated_order_price, expected);

        // 测试实付金额计算
        let calculated_actual_price = order.calculate_actual_price();
        let expected = calculated_order_price - &order.integral_price;
        assert_eq!(calculated_actual_price, expected);
    }
}
