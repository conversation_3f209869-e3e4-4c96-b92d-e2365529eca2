//! 订单仓储实现
//!
//! 提供订单相关的数据访问功能

use async_trait::async_trait;
use rust_decimal::Decimal;
use chrono::{DateTime, Utc};
use sqlx::{MySql, Pool, Row};

use crate::error::AppResult;
use crate::models::common::{PageQuery, PageResult, OrderStatus};
use crate::models::order::{
    Order, OrderQuery, UserOrderQuery, OrderDetail, OrderGoods
};
use super::base_repo::{Repository, BaseRepository, QueryBuilder};

/// 订单仓储
pub struct OrderRepository {
    base: BaseRepository,
}

impl OrderRepository {
    /// 创建新的订单仓储实例
    pub fn new(pool: Pool<MySql>) -> Self {
        Self {
            base: BaseRepository::new(pool, "dts_order".to_string()),
        }
    }

    /// 根据订单编号查找订单
    pub async fn find_by_order_sn(&self, order_sn: &str) -> AppResult<Option<Order>> {
        let sql = format!(
            "SELECT * FROM {} WHERE order_sn = ? AND deleted = 0",
            self.base.table_name
        );
        let order = sqlx::query_as::<_, Order>(&sql)
            .bind(order_sn)
            .fetch_optional(&self.base.pool)
            .await?;
        Ok(order)
    }

    /// 获取订单详情（包含订单商品）
    pub async fn find_detail_by_id(&self, order_id: i32) -> AppResult<Option<OrderDetail>> {
        // 查询订单基本信息
        let order = self.find_by_id(order_id).await?;
        if order.is_none() {
            return Ok(None);
        }
        let order = order.unwrap();

        // 查询订单商品
        let goods = self.find_order_goods_by_order_id(order_id).await?;

        Ok(Some(OrderDetail { order, goods }))
    }

    /// 查询订单商品
    pub async fn find_order_goods_by_order_id(&self, order_id: i32) -> AppResult<Vec<OrderGoods>> {
        let sql = "SELECT * FROM dts_order_goods WHERE order_id = ? AND deleted = 0 ORDER BY id";
        let goods = sqlx::query_as::<_, OrderGoods>(sql)
            .bind(order_id)
            .fetch_all(&self.base.pool)
            .await?;
        Ok(goods)
    }

    /// 更新订单状态
    pub async fn update_status(&self, order_id: i32, status: OrderStatus) -> AppResult<()> {
        let sql = format!(
            "UPDATE {} SET order_status = ?, update_time = ? WHERE id = ? AND deleted = 0",
            self.base.table_name
        );
        let now = Utc::now();
        let affected = sqlx::query(&sql)
            .bind(status as i16)
            .bind(now)
            .bind(order_id)
            .execute(&self.base.pool)
            .await?
            .rows_affected();

        if affected == 0 {
            return Err(crate::error::AppError::NotFound {
                resource: format!("Order#{}", order_id),
            });
        }

        Ok(())
    }

    /// 更新订单支付信息
    pub async fn update_payment_info(&self, order_id: i32, pay_id: &str) -> AppResult<()> {
        let sql = format!(
            "UPDATE {} SET pay_id = ?, pay_time = ?, order_status = ?, update_time = ? WHERE id = ? AND deleted = 0",
            self.base.table_name
        );
        let now = Utc::now();
        let affected = sqlx::query(&sql)
            .bind(pay_id)
            .bind(now)
            .bind(OrderStatus::Paid as i16)
            .bind(now)
            .bind(order_id)
            .execute(&self.base.pool)
            .await?
            .rows_affected();

        if affected == 0 {
            return Err(crate::error::AppError::NotFound {
                resource: format!("Order#{}", order_id),
            });
        }

        Ok(())
    }

    /// 更新订单发货信息
    pub async fn update_shipping_info(&self, order_id: i32, ship_sn: &str, ship_channel: &str) -> AppResult<()> {
        let sql = format!(
            "UPDATE {} SET ship_sn = ?, ship_channel = ?, ship_time = ?, order_status = ?, update_time = ? WHERE id = ? AND deleted = 0",
            self.base.table_name
        );
        let now = Utc::now();
        let affected = sqlx::query(&sql)
            .bind(ship_sn)
            .bind(ship_channel)
            .bind(now)
            .bind(OrderStatus::Shipped as i16)
            .bind(now)
            .bind(order_id)
            .execute(&self.base.pool)
            .await?
            .rows_affected();

        if affected == 0 {
            return Err(crate::error::AppError::NotFound {
                resource: format!("Order#{}", order_id),
            });
        }

        Ok(())
    }

    /// 确认收货
    pub async fn confirm_receipt(&self, order_id: i32) -> AppResult<()> {
        let sql = format!(
            "UPDATE {} SET confirm_time = ?, order_status = ?, update_time = ? WHERE id = ? AND deleted = 0",
            self.base.table_name
        );
        let now = Utc::now();
        let affected = sqlx::query(&sql)
            .bind(now)
            .bind(OrderStatus::Received as i16)
            .bind(now)
            .bind(order_id)
            .execute(&self.base.pool)
            .await?
            .rows_affected();

        if affected == 0 {
            return Err(crate::error::AppError::NotFound {
                resource: format!("Order#{}", order_id),
            });
        }

        Ok(())
    }

    /// 条件查询订单
    pub async fn find_by_query(&self, query: &OrderQuery) -> AppResult<PageResult<Order>> {
        let mut builder = QueryBuilder::new(&self.base.table_name);

        // 添加查询条件
        if let Some(user_id) = query.user_id {
            builder = builder.where_clause(&format!("user_id = {}", user_id));
        }
        if let Some(order_sn) = &query.order_sn {
            builder = builder.where_clause(&format!("order_sn = '{}'", order_sn));
        }
        if let Some(order_status) = query.order_status {
            builder = builder.where_clause(&format!("order_status = {}", order_status as i16));
        }
        if let Some(consignee) = &query.consignee {
            builder = builder.where_clause(&format!("consignee LIKE '%{}%'", consignee));
        }
        if let Some(mobile) = &query.mobile {
            builder = builder.where_clause(&format!("mobile LIKE '%{}%'", mobile));
        }
        if let Some(freight_type) = query.freight_type {
            builder = builder.where_clause(&format!("freight_type = {}", freight_type as i8));
        }
        if let Some(start_time) = query.start_time {
            builder = builder.where_clause(&format!("add_time >= '{}'", start_time.format("%Y-%m-%d %H:%M:%S")));
        }
        if let Some(end_time) = query.end_time {
            builder = builder.where_clause(&format!("add_time <= '{}'", end_time.format("%Y-%m-%d %H:%M:%S")));
        }

        // 添加排序
        builder = builder.order_by("add_time", "DESC");

        // 分页查询
        let count_sql = builder.build_count();
        let list_sql = builder
            .limit(query.page.limit)
            .offset(query.page.offset())
            .build();

        // 执行查询
        let count_row = sqlx::query(&count_sql).fetch_one(&self.base.pool).await?;
        let total: i64 = count_row.try_get("count")?;

        let orders = sqlx::query_as::<_, Order>(&list_sql)
            .fetch_all(&self.base.pool)
            .await?;

        Ok(PageResult::new(orders, total as u64, query.page.page, query.page.limit))
    }

    /// 用户订单查询
    pub async fn find_user_orders(&self, query: &UserOrderQuery) -> AppResult<PageResult<Order>> {
        let mut builder = QueryBuilder::new(&self.base.table_name);

        // 用户ID条件
        if let Some(user_id) = query.user_id {
            builder = builder.where_clause(&format!("user_id = {}", user_id));
        }

        // 订单状态条件
        if let Some(order_status) = query.order_status {
            builder = builder.where_clause(&format!("order_status = {}", order_status as i16));
        }

        // 添加排序
        builder = builder.order_by("add_time", "DESC");

        // 分页查询
        let count_sql = builder.build_count();
        let list_sql = builder
            .limit(query.page.limit)
            .offset(query.page.offset())
            .build();

        // 执行查询
        let count_row = sqlx::query(&count_sql).fetch_one(&self.base.pool).await?;
        let total: i64 = count_row.try_get("count")?;

        let orders = sqlx::query_as::<_, Order>(&list_sql)
            .fetch_all(&self.base.pool)
            .await?;

        Ok(PageResult::new(orders, total as u64, query.page.page, query.page.limit))
    }

    /// 统计订单状态分布
    pub async fn count_by_status(&self) -> AppResult<Vec<(OrderStatus, u64)>> {
        let sql = format!(
            "SELECT order_status, COUNT(*) as count FROM {} WHERE deleted = 0 GROUP BY order_status",
            self.base.table_name
        );
        let rows = sqlx::query(&sql).fetch_all(&self.base.pool).await?;
        
        let mut result = Vec::new();
        for row in rows {
            let status: i16 = row.try_get("order_status")?;
            let count: i64 = row.try_get("count")?;
            result.push((OrderStatus::from(status), count as u64));
        }
        
        Ok(result)
    }

    /// 统计用户订单数量
    pub async fn count_user_orders(&self, user_id: i32, status: Option<OrderStatus>) -> AppResult<u64> {
        let sql = if let Some(_status) = status {
            format!(
                "SELECT COUNT(*) as count FROM {} WHERE user_id = ? AND deleted = 0 AND order_status = ?",
                self.base.table_name
            )
        } else {
            format!(
                "SELECT COUNT(*) as count FROM {} WHERE user_id = ? AND deleted = 0",
                self.base.table_name
            )
        };

        let mut query = sqlx::query(&sql).bind(user_id);

        if let Some(status) = status {
            query = query.bind(status as i16);
        }

        let row = query.fetch_one(&self.base.pool).await?;
        let count: i64 = row.try_get("count")?;
        Ok(count as u64)
    }

    /// 计算用户总消费金额
    pub async fn calculate_user_total_amount(&self, user_id: i32) -> AppResult<Decimal> {
        let sql = format!(
            "SELECT COALESCE(SUM(actual_price), 0) as total FROM {} WHERE user_id = ? AND order_status IN (?, ?, ?) AND deleted = 0",
            self.base.table_name
        );
        let row = sqlx::query(&sql)
            .bind(user_id)
            .bind(OrderStatus::Paid as i16)
            .bind(OrderStatus::Shipped as i16)
            .bind(OrderStatus::Received as i16)
            .fetch_one(&self.base.pool)
            .await?;
        let total: Decimal = row.try_get("total")?;
        Ok(total)
    }

    /// 生成订单编号
    pub async fn generate_order_sn(&self) -> AppResult<String> {
        self.base.generate_unique_sn("O").await
    }

    /// 生成提货码
    pub async fn generate_fetch_code(&self) -> AppResult<String> {
        use rand::Rng;
        let mut rng = rand::thread_rng();
        let code: u32 = rng.gen_range(100000..999999);
        Ok(format!("{:06}", code))
    }
}

#[async_trait]
impl Repository<Order> for OrderRepository {
    async fn find_by_id(&self, id: i32) -> AppResult<Option<Order>> {
        let sql = format!(
            "SELECT * FROM {} WHERE id = ? AND deleted = 0",
            self.base.table_name
        );
        let order = sqlx::query_as::<_, Order>(&sql)
            .bind(id)
            .fetch_optional(&self.base.pool)
            .await?;
        Ok(order)
    }

    async fn create(&self, order: &Order) -> AppResult<i32> {
        let sql = "INSERT INTO dts_order (user_id, order_sn, order_status, consignee, mobile, address, 
                   message, goods_price, freight_price, coupon_price, integral_price, groupon_price, 
                   order_price, actual_price, pay_id, pay_time, ship_sn, ship_channel, ship_time, 
                   confirm_time, comments, end_time, add_time, update_time, deleted, settlement_money, 
                   settlement_status, freight_type, share_user_id, fetch_code, create_user_id, 
                   gift_send_time, gift_receive_time) 
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        let now = Utc::now();
        let result = sqlx::query(sql)
            .bind(order.user_id)
            .bind(&order.order_sn)
            .bind(order.order_status as i16)
            .bind(&order.consignee)
            .bind(&order.mobile)
            .bind(&order.address)
            .bind(&order.message)
            .bind(&order.goods_price)
            .bind(&order.freight_price)
            .bind(&order.coupon_price)
            .bind(&order.integral_price)
            .bind(&order.groupon_price)
            .bind(&order.order_price)
            .bind(&order.actual_price)
            .bind(&order.pay_id)
            .bind(order.pay_time)
            .bind(&order.ship_sn)
            .bind(&order.ship_channel)
            .bind(order.ship_time)
            .bind(order.confirm_time)
            .bind(order.comments)
            .bind(order.end_time)
            .bind(now)
            .bind(now)
            .bind(false)
            .bind(&order.settlement_money)
            .bind(order.settlement_status)
            .bind(order.freight_type as i8)
            .bind(order.share_user_id)
            .bind(&order.fetch_code)
            .bind(order.create_user_id)
            .bind(order.gift_send_time)
            .bind(order.gift_receive_time)
            .execute(&self.base.pool)
            .await?;
        
        Ok(result.last_insert_id() as i32)
    }

    async fn update(&self, order: &Order) -> AppResult<()> {
        let sql = "UPDATE dts_order SET user_id = ?, order_sn = ?, order_status = ?, consignee = ?, 
                   mobile = ?, address = ?, message = ?, goods_price = ?, freight_price = ?, 
                   coupon_price = ?, integral_price = ?, groupon_price = ?, order_price = ?, 
                   actual_price = ?, pay_id = ?, pay_time = ?, ship_sn = ?, ship_channel = ?, 
                   ship_time = ?, confirm_time = ?, comments = ?, end_time = ?, update_time = ?, 
                   settlement_money = ?, settlement_status = ?, freight_type = ?, share_user_id = ?, 
                   fetch_code = ?, create_user_id = ?, gift_send_time = ?, gift_receive_time = ? 
                   WHERE id = ? AND deleted = 0";
        
        let now = Utc::now();
        let affected = sqlx::query(sql)
            .bind(order.user_id)
            .bind(&order.order_sn)
            .bind(order.order_status as i16)
            .bind(&order.consignee)
            .bind(&order.mobile)
            .bind(&order.address)
            .bind(&order.message)
            .bind(&order.goods_price)
            .bind(&order.freight_price)
            .bind(&order.coupon_price)
            .bind(&order.integral_price)
            .bind(&order.groupon_price)
            .bind(&order.order_price)
            .bind(&order.actual_price)
            .bind(&order.pay_id)
            .bind(order.pay_time)
            .bind(&order.ship_sn)
            .bind(&order.ship_channel)
            .bind(order.ship_time)
            .bind(order.confirm_time)
            .bind(order.comments)
            .bind(order.end_time)
            .bind(now)
            .bind(&order.settlement_money)
            .bind(order.settlement_status)
            .bind(order.freight_type as i8)
            .bind(order.share_user_id)
            .bind(&order.fetch_code)
            .bind(order.create_user_id)
            .bind(order.gift_send_time)
            .bind(order.gift_receive_time)
            .bind(order.id)
            .execute(&self.base.pool)
            .await?
            .rows_affected();

        if affected == 0 {
            return Err(crate::error::AppError::NotFound {
                resource: format!("Order#{}", order.id),
            });
        }

        Ok(())
    }

    async fn delete(&self, id: i32) -> AppResult<()> {
        self.base.execute_soft_delete(id).await
    }

    async fn delete_permanently(&self, id: i32) -> AppResult<()> {
        self.base.execute_hard_delete(id).await
    }

    async fn delete_batch(&self, ids: &[i32]) -> AppResult<u64> {
        self.base.execute_batch_soft_delete(ids).await
    }

    async fn exists(&self, id: i32) -> AppResult<bool> {
        self.base.check_exists(id).await
    }

    async fn count(&self) -> AppResult<u64> {
        self.base.execute_count(&self.base.base_where_clause()).await
    }

    async fn find_page(&self, page: &PageQuery) -> AppResult<PageResult<Order>> {
        let count_sql = format!(
            "SELECT COUNT(*) as count FROM {} WHERE deleted = 0",
            self.base.table_name
        );
        let list_sql = format!(
            "SELECT * FROM {} WHERE deleted = 0 ORDER BY add_time DESC LIMIT {} OFFSET {}",
            self.base.table_name, page.limit, page.offset()
        );

        let count_row = sqlx::query(&count_sql).fetch_one(&self.base.pool).await?;
        let total: i64 = count_row.try_get("count")?;

        let orders = sqlx::query_as::<_, Order>(&list_sql)
            .fetch_all(&self.base.pool)
            .await?;

        Ok(PageResult::new(orders, total as u64, page.page, page.limit))
    }
}

impl OrderRepository {
    /// 创建订单商品
    pub async fn create_order_goods(&self, order_goods: &OrderGoods) -> AppResult<i32> {
        let sql = "INSERT INTO dts_order_goods
            (order_id, goods_id, goods_name, goods_sn, product_id, number, price, specifications, pic_url, comment, add_time, update_time, deleted, settlement_money)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        let result = sqlx::query(sql)
            .bind(&order_goods.order_id)
            .bind(&order_goods.goods_id)
            .bind(&order_goods.goods_name)
            .bind(&order_goods.goods_sn)
            .bind(&order_goods.product_id)
            .bind(&order_goods.number)
            .bind(&order_goods.price)
            .bind(serde_json::to_string(&order_goods.specifications).unwrap_or_default())
            .bind(&order_goods.pic_url)
            .bind(&order_goods.comment)
            .bind(&order_goods.add_time)
            .bind(&order_goods.update_time)
            .bind(&order_goods.deleted)
            .bind(&order_goods.settlement_money)
            .execute(&self.base.pool)
            .await?;

        Ok(result.last_insert_id() as i32)
    }

    /// 查找订单商品
    pub async fn find_order_goods(&self, order_id: i32) -> AppResult<Vec<OrderGoods>> {
        let sql = "SELECT * FROM dts_order_goods WHERE order_id = ? AND deleted = 0 ORDER BY id";

        let goods_list = sqlx::query_as::<_, OrderGoods>(sql)
            .bind(order_id)
            .fetch_all(&self.base.pool)
            .await?;

        Ok(goods_list)
    }





    /// 更新确认收货时间
    pub async fn update_confirm_time(&self, order_id: i32, confirm_time: &DateTime<Utc>) -> AppResult<()> {
        let sql = format!(
            "UPDATE {} SET confirm_time = ?, update_time = ? WHERE id = ? AND deleted = 0",
            self.base.table_name
        );

        sqlx::query(&sql)
            .bind(confirm_time)
            .bind(Utc::now())
            .bind(order_id)
            .execute(&self.base.pool)
            .await?;

        Ok(())
    }

    /// 统计订单总数
    pub async fn count_orders(&self) -> AppResult<u64> {
        let sql = format!(
            "SELECT COUNT(*) as count FROM {} WHERE deleted = 0",
            self.base.table_name
        );

        let row = sqlx::query(&sql)
            .fetch_one(&self.base.pool)
            .await?;
        let count: i64 = row.try_get("count")?;
        Ok(count as u64)
    }

    /// 按状态统计订单数
    pub async fn count_orders_by_status(&self, status: OrderStatus) -> AppResult<u64> {
        let sql = format!(
            "SELECT COUNT(*) as count FROM {} WHERE order_status = ? AND deleted = 0",
            self.base.table_name
        );

        let row = sqlx::query(&sql)
            .bind(status as i8)
            .fetch_one(&self.base.pool)
            .await?;
        let count: i64 = row.try_get("count")?;
        Ok(count as u64)
    }

    /// 计算总交易金额
    pub async fn calculate_total_amount(&self) -> AppResult<Decimal> {
        let sql = format!(
            "SELECT COALESCE(SUM(actual_price), 0) as total FROM {} WHERE order_status >= ? AND deleted = 0",
            self.base.table_name
        );

        let row = sqlx::query(&sql)
            .bind(OrderStatus::Paid as i8)
            .fetch_one(&self.base.pool)
            .await?;
        let total: Decimal = row.try_get("total")?;
        Ok(total)
    }
}
