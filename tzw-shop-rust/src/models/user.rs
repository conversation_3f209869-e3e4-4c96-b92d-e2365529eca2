//! 用户相关数据模型

use rust_decimal::Decimal;
use chrono::{DateTime, NaiveDate, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use validator::Validate;

use super::common::{BaseEntity, Gender, UserLevel, UserStatus, PageQuery};

/// 用户实体
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct User {
    pub id: i32,
    /// 用户名称
    pub username: String,
    /// 用户密码（加密后）
    pub password: String,
    /// 性别：0 未知， 1男， 2 女
    pub gender: Gender,
    /// 生日
    pub birthday: Option<NaiveDate>,
    /// 最近一次登录时间
    pub last_login_time: Option<DateTime<Utc>>,
    /// 最近一次登录IP地址
    pub last_login_ip: String,
    /// 用户层级
    pub user_level: UserLevel,
    /// 用户昵称或网络名称
    pub nickname: String,
    /// 用户手机号码
    pub mobile: String,
    /// 用户头像图片
    pub avatar: String,
    /// 微信登录openid
    pub weixin_openid: String,
    /// 用户状态
    pub status: UserStatus,
    /// 创建时间
    pub add_time: Option<DateTime<Utc>>,
    /// 更新时间
    pub update_time: Option<DateTime<Utc>>,
    /// 逻辑删除
    pub deleted: bool,
    /// 推广用户ID
    pub share_user_id: Option<i32>,
}

impl BaseEntity for User {
    fn id(&self) -> i32 {
        self.id
    }

    fn add_time(&self) -> Option<DateTime<Utc>> {
        self.add_time
    }

    fn update_time(&self) -> Option<DateTime<Utc>> {
        self.update_time
    }

    fn is_deleted(&self) -> bool {
        self.deleted
    }
}

/// 用户创建请求
#[derive(Debug, Clone, Deserialize, Validate)]
pub struct CreateUserRequest {
    #[validate(length(min = 2, max = 63, message = "用户名长度必须在2-63个字符之间"))]
    pub username: String,
    
    #[validate(length(min = 6, max = 20, message = "密码长度必须在6-20个字符之间"))]
    pub password: String,
    
    pub gender: Option<Gender>,
    pub birthday: Option<NaiveDate>,
    
    #[validate(length(max = 63, message = "昵称长度不能超过63个字符"))]
    pub nickname: Option<String>,
    
    #[validate(length(min = 11, max = 11, message = "手机号必须为11位数字"))]
    pub mobile: String,
    
    pub avatar: Option<String>,
    pub weixin_openid: Option<String>,
    pub share_user_id: Option<i32>,
}

/// 用户更新请求
#[derive(Debug, Clone, Deserialize, Validate)]
pub struct UpdateUserRequest {
    pub gender: Option<Gender>,
    pub birthday: Option<NaiveDate>,
    
    #[validate(length(max = 63, message = "昵称长度不能超过63个字符"))]
    pub nickname: Option<String>,
    
    #[validate(length(min = 11, max = 11, message = "手机号必须为11位数字"))]
    pub mobile: Option<String>,
    
    pub avatar: Option<String>,
}

/// 用户查询参数
#[derive(Debug, Clone, Deserialize)]
pub struct UserQuery {
    #[serde(flatten)]
    pub page: PageQuery,
    /// 用户名模糊查询
    pub username: Option<String>,
    /// 手机号模糊查询
    pub mobile: Option<String>,
    /// 用户状态
    pub status: Option<UserStatus>,
    /// 用户层级
    pub user_level: Option<UserLevel>,
    /// 性别
    pub gender: Option<Gender>,
    /// 注册开始时间
    pub start_time: Option<DateTime<Utc>>,
    /// 注册结束时间
    pub end_time: Option<DateTime<Utc>>,
}

/// 用户响应信息（不包含敏感信息）
#[derive(Debug, Clone, Serialize)]
pub struct UserInfo {
    pub id: i32,
    pub username: String,
    pub gender: Gender,
    pub birthday: Option<NaiveDate>,
    pub last_login_time: Option<DateTime<Utc>>,
    pub user_level: UserLevel,
    pub nickname: String,
    pub mobile: String,
    pub avatar: String,
    pub status: UserStatus,
    pub add_time: Option<DateTime<Utc>>,
}

impl From<User> for UserInfo {
    fn from(user: User) -> Self {
        Self {
            id: user.id,
            username: user.username,
            gender: user.gender,
            birthday: user.birthday,
            last_login_time: user.last_login_time,
            user_level: user.user_level,
            nickname: user.nickname,
            mobile: user.mobile,
            avatar: user.avatar,
            status: user.status,
            add_time: user.add_time,
        }
    }
}

/// 用户地址实体
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct Address {
    pub id: i32,
    /// 收货人名称
    pub name: String,
    /// 用户ID
    pub user_id: i32,
    /// 省份ID
    pub province_id: i32,
    /// 城市ID
    pub city_id: i32,
    /// 区县ID
    pub area_id: i32,
    /// 具体收货地址
    pub address: String,
    /// 手机号码
    pub mobile: String,
    /// 是否默认地址
    pub is_default: bool,
    /// 创建时间
    pub add_time: Option<DateTime<Utc>>,
    /// 更新时间
    pub update_time: Option<DateTime<Utc>>,
    /// 逻辑删除
    pub deleted: bool,
}

impl BaseEntity for Address {
    fn id(&self) -> i32 {
        self.id
    }

    fn add_time(&self) -> Option<DateTime<Utc>> {
        self.add_time
    }

    fn update_time(&self) -> Option<DateTime<Utc>> {
        self.update_time
    }

    fn is_deleted(&self) -> bool {
        self.deleted
    }
}

impl Address {
    /// 获取完整地址
    pub fn full_address(&self) -> String {
        // TODO: 需要根据省市区ID查询名称
        self.address.clone()
    }
}

/// 地址创建请求
#[derive(Debug, Clone, Deserialize, Validate)]
pub struct CreateAddressRequest {
    #[validate(length(min = 1, max = 63, message = "收货人姓名不能为空且不超过63个字符"))]
    pub name: String,
    
    pub province_id: i32,
    pub city_id: i32,
    pub area_id: i32,
    
    #[validate(length(min = 1, max = 127, message = "详细地址不能为空且不超过127个字符"))]
    pub address: String,
    
    #[validate(length(min = 11, max = 11, message = "手机号必须为11位数字"))]
    pub mobile: String,
    
    pub is_default: Option<bool>,
}

/// 地址更新请求
#[derive(Debug, Clone, Deserialize, Validate)]
pub struct UpdateAddressRequest {
    #[validate(length(min = 1, max = 63, message = "收货人姓名不能为空且不超过63个字符"))]
    pub name: Option<String>,
    
    pub province_id: Option<i32>,
    pub city_id: Option<i32>,
    pub area_id: Option<i32>,
    
    #[validate(length(min = 1, max = 127, message = "详细地址不能为空且不超过127个字符"))]
    pub address: Option<String>,
    
    #[validate(length(min = 11, max = 11, message = "手机号必须为11位数字"))]
    pub mobile: Option<String>,
    
    pub is_default: Option<bool>,
}

/// 用户账户实体
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct UserAccount {
    pub id: i32,
    /// 用户ID
    pub user_id: i32,
    /// 账户余额
    pub remain_amount: Option<Decimal>,
    /// 账户总额
    pub total_amount: Option<Decimal>,
    /// 创建时间
    pub create_time: Option<DateTime<Utc>>,
    /// 修改时间
    pub modify_time: Option<DateTime<Utc>>,
    /// 结算利率（百分比）
    pub settlement_rate: Option<i8>,
    /// 账户状态
    pub status: Option<i8>,
    /// 分享推广二维码URL
    pub share_url: Option<String>,
}

/// 账户流水实体
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct AccountTrace {
    pub id: i32,
    /// 操作流水号
    pub trace_sn: Option<String>,
    /// 用户ID
    pub user_id: i32,
    /// 操作类型：0系统结算 1用户提现
    pub r#type: Option<i8>,
    /// 操作金额
    pub amount: Option<Decimal>,
    /// 总申请金额
    pub total_amount: Option<Decimal>,
    /// 申请时间
    pub add_time: Option<DateTime<Utc>>,
    /// 手机号
    pub mobile: Option<String>,
    /// 短信提取码
    pub sms_code: Option<String>,
    /// 审批状态
    pub status: Option<i8>,
    /// 消息内容
    pub trace_msg: Option<String>,
    /// 审批时间
    pub update_time: Option<DateTime<Utc>>,
}

/// 微信登录请求
#[derive(Debug, Clone, Deserialize, Validate)]
pub struct WxLoginRequest {
    #[validate(length(min = 1, message = "微信授权码不能为空"))]
    pub js_code: String,
    pub user_info: Option<WxUserInfo>,
}

/// 微信用户信息
#[derive(Debug, Clone, Deserialize)]
pub struct WxUserInfo {
    pub nickname: String,
    pub avatar_url: String,
    pub gender: i8,
    pub country: String,
    pub province: String,
    pub city: String,
    pub language: String,
}

/// 微信登录响应
#[derive(Debug, Clone, Serialize)]
pub struct WxLoginResponse {
    pub access_token: String,
    pub refresh_token: String,
    pub user_info: UserInfo,
}

/// 管理员登录请求
#[derive(Debug, Clone, Deserialize, Validate)]
pub struct AdminLoginRequest {
    #[validate(length(min = 1, max = 63, message = "用户名不能为空且不超过63个字符"))]
    pub username: String,
    
    #[validate(length(min = 1, message = "密码不能为空"))]
    pub password: String,
}

/// 管理员登录响应
#[derive(Debug, Clone, Serialize)]
pub struct AdminLoginResponse {
    pub access_token: String,
    pub refresh_token: String,
    pub expires_in: i64,
}
