//! 订单相关数据模型

use rust_decimal::Decimal;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use validator::Validate;

use super::common::{BaseEntity, OrderStatus, FreightType, PageQuery};

/// 订单实体
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct Order {
    pub id: i32,
    /// 用户ID
    pub user_id: i32,
    /// 订单编号
    pub order_sn: String,
    /// 订单状态
    pub order_status: OrderStatus,
    /// 收货人名称
    pub consignee: String,
    /// 收货人手机号
    pub mobile: String,
    /// 收货具体地址
    pub address: String,
    /// 用户订单留言
    pub message: String,
    /// 商品总费用
    pub goods_price: Decimal,
    /// 配送费用
    pub freight_price: Option<Decimal>,
    /// 优惠券减免
    pub coupon_price: Decimal,
    /// 用户积分减免
    pub integral_price: Decimal,
    /// 团购优惠价减免
    pub groupon_price: Decimal,
    /// 订单费用
    pub order_price: Decimal,
    /// 实付费用
    pub actual_price: Decimal,
    /// 微信付款编号
    pub pay_id: Option<String>,
    /// 微信付款时间
    pub pay_time: Option<DateTime<Utc>>,
    /// 发货编号
    pub ship_sn: Option<String>,
    /// 发货快递公司
    pub ship_channel: Option<String>,
    /// 发货开始时间
    pub ship_time: Option<DateTime<Utc>>,
    /// 用户确认收货时间
    pub confirm_time: Option<DateTime<Utc>>,
    /// 待评价订单商品数量
    pub comments: Option<i16>,
    /// 订单关闭时间
    pub end_time: Option<DateTime<Utc>>,
    /// 创建时间
    pub add_time: Option<DateTime<Utc>>,
    /// 更新时间
    pub update_time: Option<DateTime<Utc>>,
    /// 逻辑删除
    pub deleted: bool,
    /// 代理结算金额
    pub settlement_money: Option<Decimal>,
    /// 结算状态
    pub settlement_status: bool,
    /// 配送方式：0 快递, 1 自提
    pub freight_type: FreightType,
    /// 推广用户
    pub share_user_id: Option<i32>,
    /// 提货码
    pub fetch_code: Option<String>,
    /// 原始创建人
    pub create_user_id: Option<i32>,
    /// 转赠发送时间
    pub gift_send_time: Option<DateTime<Utc>>,
    /// 转赠接收时间
    pub gift_receive_time: Option<DateTime<Utc>>,
}

impl BaseEntity for Order {
    fn id(&self) -> i32 {
        self.id
    }

    fn add_time(&self) -> Option<DateTime<Utc>> {
        self.add_time
    }

    fn update_time(&self) -> Option<DateTime<Utc>> {
        self.update_time
    }

    fn is_deleted(&self) -> bool {
        self.deleted
    }
}

impl Order {
    /// 计算订单总价
    pub fn calculate_order_price(&self) -> Decimal {
        &self.goods_price + self.freight_price.as_ref().unwrap_or(&Decimal::from(0)) - &self.coupon_price - &self.groupon_price
    }

    /// 计算实付金额
    pub fn calculate_actual_price(&self) -> Decimal {
        self.calculate_order_price() - &self.integral_price
    }

    /// 检查订单是否可以取消
    pub fn can_cancel(&self) -> bool {
        self.order_status.can_cancel()
    }

    /// 检查订单是否可以支付
    pub fn can_pay(&self) -> bool {
        self.order_status.can_pay()
    }

    /// 检查订单是否可以发货
    pub fn can_ship(&self) -> bool {
        self.order_status.can_ship()
    }

    /// 检查订单是否可以确认收货
    pub fn can_confirm(&self) -> bool {
        self.order_status.can_confirm()
    }

    /// 检查订单是否可以评价
    pub fn can_comment(&self) -> bool {
        self.order_status.can_comment()
    }
}

/// 订单商品实体
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct OrderGoods {
    pub id: i32,
    /// 订单ID
    pub order_id: i32,
    /// 入驻品牌店铺编码
    pub brand_id: Option<i32>,
    /// 商品ID
    pub goods_id: i32,
    /// 商品名称
    pub goods_name: String,
    /// 商品编号
    pub goods_sn: String,
    /// 商品货品ID
    pub product_id: i32,
    /// 商品货品的购买数量
    pub number: i16,
    /// 商品货品的售价
    pub price: f64,
    /// 商品货品的规格列表
    #[sqlx(json)]
    pub specifications: Vec<String>,
    /// 商品货品图片或者商品图片
    pub pic_url: String,
    /// 订单商品评论ID
    pub comment: Option<i32>,
    /// 创建时间
    pub add_time: Option<DateTime<Utc>>,
    /// 更新时间
    pub update_time: Option<DateTime<Utc>>,
    /// 逻辑删除
    pub deleted: bool,
    /// 退款跟踪ID
    pub refund_id: Option<i32>,
    /// 代理结算佣金
    pub settlement_money: Option<f64>,
}

/// 购物车实体
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct Cart {
    pub id: i32,
    /// 用户ID
    pub user_id: Option<i32>,
    /// 入驻品牌商编码
    pub brand_id: Option<i32>,
    /// 商品ID
    pub goods_id: Option<i32>,
    /// 商品编号
    pub goods_sn: Option<String>,
    /// 商品名称
    pub goods_name: Option<String>,
    /// 商品货品ID
    pub product_id: Option<i32>,
    /// 商品货品的价格
    pub price: Option<Decimal>,
    /// 商品货品的数量
    pub number: Option<i16>,
    /// 商品规格值列表，JSON数组格式
    #[sqlx(json)]
    pub specifications: Option<Vec<String>>,
    /// 购物车中商品是否选择状态
    pub checked: Option<bool>,
    /// 商品图片或者商品货品图片
    pub pic_url: Option<String>,
    /// 创建时间
    pub add_time: Option<DateTime<Utc>>,
    /// 更新时间
    pub update_time: Option<DateTime<Utc>>,
    /// 逻辑删除
    pub deleted: bool,
    /// 代理结算佣金
    pub settlement_money: Option<Decimal>,
}

impl BaseEntity for Cart {
    fn id(&self) -> i32 {
        self.id
    }

    fn add_time(&self) -> Option<DateTime<Utc>> {
        self.add_time
    }

    fn update_time(&self) -> Option<DateTime<Utc>> {
        self.update_time
    }

    fn is_deleted(&self) -> bool {
        self.deleted
    }
}

/// 订单详情（包含订单商品）
#[derive(Debug, Clone, Serialize)]
pub struct OrderDetail {
    pub order: Order,
    pub goods: Vec<OrderGoods>,
}

/// 订单创建请求
#[derive(Debug, Clone, Deserialize, Validate)]
pub struct CreateOrderRequest {
    /// 收货地址ID
    pub address_id: i32,
    /// 优惠券ID
    pub coupon_id: Option<i32>,
    /// 订单留言
    #[validate(length(max = 512, message = "订单留言不能超过512个字符"))]
    pub message: Option<String>,
    /// 配送方式
    pub freight_type: Option<FreightType>,
    /// 用户ID（从认证中获取）
    #[serde(skip)]
    pub user_id: i32,
}

/// 订单查询参数
#[derive(Debug, Clone, Deserialize)]
pub struct OrderQuery {
    #[serde(flatten)]
    pub page: PageQuery,
    /// 用户ID
    pub user_id: Option<i32>,
    /// 订单编号
    pub order_sn: Option<String>,
    /// 订单状态
    pub order_status: Option<OrderStatus>,
    /// 收货人姓名
    pub consignee: Option<String>,
    /// 收货人手机号
    pub mobile: Option<String>,
    /// 配送方式
    pub freight_type: Option<FreightType>,
    /// 开始时间
    pub start_time: Option<DateTime<Utc>>,
    /// 结束时间
    pub end_time: Option<DateTime<Utc>>,
}

/// 用户订单查询参数
#[derive(Debug, Clone, Deserialize)]
pub struct UserOrderQuery {
    #[serde(flatten)]
    pub page: PageQuery,
    /// 订单状态
    pub order_status: Option<OrderStatus>,
    /// 用户ID（从认证中获取）
    #[serde(skip)]
    pub user_id: Option<i32>,
}

/// 添加到购物车请求
#[derive(Debug, Clone, Deserialize, Validate)]
pub struct AddToCartRequest {
    /// 商品ID
    pub goods_id: i32,
    /// 商品货品ID
    pub product_id: i32,
    /// 数量
    #[validate(range(min = 1, max = 999, message = "商品数量必须在1-999之间"))]
    pub number: i16,
    /// 商品规格
    pub specifications: Vec<String>,
    /// 用户ID（从认证中获取）
    #[serde(skip)]
    pub user_id: i32,
}

/// 更新购物车请求
#[derive(Debug, Clone, Deserialize, Validate)]
pub struct UpdateCartRequest {
    /// 数量
    #[validate(range(min = 1, max = 999, message = "商品数量必须在1-999之间"))]
    pub number: Option<i16>,
    /// 是否选中
    pub checked: Option<bool>,
}

/// 发货请求
#[derive(Debug, Clone, Deserialize, Validate)]
pub struct ShipOrderRequest {
    /// 发货编号
    #[validate(length(min = 1, max = 63, message = "发货编号不能为空且不超过63个字符"))]
    pub ship_sn: String,
    /// 快递公司
    #[validate(length(min = 1, max = 63, message = "快递公司不能为空且不超过63个字符"))]
    pub ship_channel: String,
}

/// 退款跟踪实体
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct RefundTrace {
    pub id: i32,
    /// 订单ID
    pub order_id: i32,
    /// 订单编号
    pub order_sn: String,
    /// 审批状态：0 退款申请 1 退款审批通过 2 退款审批拒绝 3 退款完成
    pub status: Option<i8>,
    /// 退款原因
    pub refund_reason: Option<String>,
    /// 是否含有图片
    pub has_picture: Option<bool>,
    /// 退款质量图片地址列表，JSON数组格式
    #[sqlx(json)]
    pub pic_urls: Option<Vec<String>>,
    /// 创建时间，即退款申请时间
    pub add_time: Option<DateTime<Utc>>,
    /// 审批内容
    pub approve_msg: Option<String>,
    /// 审批时间
    pub approve_time: Option<DateTime<Utc>>,
    /// 退款物流内容
    pub freight_msg: Option<String>,
    /// 退款时间
    pub refund_time: Option<DateTime<Utc>>,
    /// 更新时间
    pub update_time: Option<DateTime<Utc>>,
    /// 逻辑删除
    pub deleted: bool,
}

/// 退款状态枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize, sqlx::Type)]
#[repr(i8)]
pub enum RefundStatus {
    /// 退款申请
    Applied = 0,
    /// 退款审批通过
    Approved = 1,
    /// 退款审批拒绝
    Rejected = 2,
    /// 退款完成
    Completed = 3,
}

impl Default for RefundStatus {
    fn default() -> Self {
        Self::Applied
    }
}

impl From<i8> for RefundStatus {
    fn from(value: i8) -> Self {
        match value {
            0 => Self::Applied,
            1 => Self::Approved,
            2 => Self::Rejected,
            3 => Self::Completed,
            _ => Self::Applied,
        }
    }
}

/// 支付预下单响应
#[derive(Debug, Clone, Serialize)]
pub struct PrepayResponse {
    pub prepay_id: String,
    pub pay_sign: String,
    pub timestamp: String,
    pub nonce_str: String,
    pub sign_type: String,
}

/// 支付通知数据
#[derive(Debug, Clone, Deserialize)]
pub struct PaymentNotify {
    pub return_code: String,
    pub return_msg: String,
    pub result_code: String,
    pub err_code_des: Option<String>,
    pub out_trade_no: String,
    pub transaction_id: String,
    pub total_fee: i32,
    pub time_end: String,
}
