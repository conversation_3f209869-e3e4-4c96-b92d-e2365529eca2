//! 商品相关数据模型

use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

use super::common::{BaseEntity, ApproveStatus, BrokerageType, PageQuery};

/// 商品实体
#[derive(Debu<PERSON>, <PERSON>lone, FromRow, Serialize, Deserialize)]
pub struct Goods {
    pub id: i32,
    /// 商品编号
    pub goods_sn: String,
    /// 商品名称
    pub name: String,
    /// 商品所属类目ID
    pub category_id: Option<i32>,
    /// 品牌ID
    pub brand_id: Option<i32>,
    /// 商品宣传图片列表，JSON数组格式
    #[sqlx(json)]
    pub gallery: Option<Vec<String>>,
    /// 商品关键字，逗号分隔
    pub keywords: Option<String>,
    /// 商品简介
    pub brief: Option<String>,
    /// 是否上架
    pub is_on_sale: Option<bool>,
    /// 排序
    pub sort_order: Option<i16>,
    /// 商品页面商品图片
    pub pic_url: Option<String>,
    /// 商品分享朋友圈图片
    pub share_url: Option<String>,
    /// 是否新品首发
    pub is_new: Option<bool>,
    /// 是否人气推荐
    pub is_hot: Option<bool>,
    /// 商品单位
    pub unit: Option<String>,
    /// 专柜价格
    pub counter_price: Option<Decimal>,
    /// 零售价格
    pub retail_price: Option<Decimal>,
    /// 商品详细介绍，富文本格式
    pub detail: Option<String>,
    /// 创建时间
    pub add_time: Option<DateTime<Utc>>,
    /// 更新时间
    pub update_time: Option<DateTime<Utc>>,
    /// 浏览量
    pub browse: i32,
    /// 已销售总量
    pub sales: i32,
    /// 逻辑删除
    pub deleted: bool,
    /// 供货单位
    pub commpany: Option<String>,
    /// 批发价格
    pub wholesale_price: Option<Decimal>,
    /// 审批状态
    pub approve_status: ApproveStatus,
    /// 审批内容
    pub approve_msg: Option<String>,
    /// 佣金类型
    pub brokerage_type: BrokerageType,
    /// 推广佣金金额
    pub brokerage_price: Option<Decimal>,
}

impl BaseEntity for Goods {
    fn id(&self) -> i32 {
        self.id
    }

    fn add_time(&self) -> Option<DateTime<Utc>> {
        self.add_time
    }

    fn update_time(&self) -> Option<DateTime<Utc>> {
        self.update_time
    }

    fn is_deleted(&self) -> bool {
        self.deleted
    }
}

/// 商品规格实体
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct GoodsSpecification {
    pub id: i32,
    /// 商品ID
    pub goods_id: i32,
    /// 商品规格名称
    pub specification: String,
    /// 商品规格值
    pub value: String,
    /// 商品规格图片
    pub pic_url: String,
    /// 创建时间
    pub add_time: Option<DateTime<Utc>>,
    /// 更新时间
    pub update_time: Option<DateTime<Utc>>,
    /// 逻辑删除
    pub deleted: bool,
}

/// 商品货品实体（SKU）
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct GoodsProduct {
    pub id: i32,
    /// 商品ID
    pub goods_id: i32,
    /// 商品规格值列表，JSON数组格式
    #[sqlx(json)]
    pub specifications: Vec<String>,
    /// 商品货品价格
    pub price: Decimal,
    /// 商品货品数量
    pub number: i32,
    /// 商品货品图片
    pub url: Option<String>,
    /// 创建时间
    pub add_time: Option<DateTime<Utc>>,
    /// 更新时间
    pub update_time: Option<DateTime<Utc>>,
    /// 逻辑删除
    pub deleted: bool,
}

/// 商品属性实体
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct GoodsAttribute {
    pub id: i32,
    /// 商品ID
    pub goods_id: i32,
    /// 商品参数名称
    pub attribute: String,
    /// 商品参数值
    pub value: String,
    /// 创建时间
    pub add_time: Option<DateTime<Utc>>,
    /// 更新时间
    pub update_time: Option<DateTime<Utc>>,
    /// 逻辑删除
    pub deleted: bool,
}

/// 商品分类实体
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct Category {
    pub id: i32,
    /// 类目名称
    pub name: String,
    /// 类目关键字，JSON数组格式
    #[sqlx(json)]
    pub keywords: Vec<String>,
    /// 类目广告语介绍
    pub desc: Option<String>,
    /// 父类目ID
    pub pid: i32,
    /// 类目图标
    pub icon_url: Option<String>,
    /// 类目图片
    pub pic_url: Option<String>,
    /// 类目层级
    pub level: Option<String>,
    /// 排序
    pub sort_order: Option<i8>,
    /// 创建时间
    pub add_time: Option<DateTime<Utc>>,
    /// 更新时间
    pub update_time: Option<DateTime<Utc>>,
    /// 逻辑删除
    pub deleted: bool,
}

impl BaseEntity for Category {
    fn id(&self) -> i32 {
        self.id
    }

    fn add_time(&self) -> Option<DateTime<Utc>> {
        self.add_time
    }

    fn update_time(&self) -> Option<DateTime<Utc>> {
        self.update_time
    }

    fn is_deleted(&self) -> bool {
        self.deleted
    }
}

/// 品牌实体
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct Brand {
    pub id: i32,
    /// 品牌商名称
    pub name: String,
    /// 品牌商简介
    pub desc: String,
    /// 品牌商页的品牌商图片
    pub pic_url: String,
    /// 排序
    pub sort_order: Option<i8>,
    /// 品牌商的商品低价，仅用于页面展示
    pub floor_price: Option<Decimal>,
    /// 创建时间
    pub add_time: Option<DateTime<Utc>>,
    /// 更新时间
    pub update_time: Option<DateTime<Utc>>,
    /// 分享二维码图片
    pub share_url: Option<String>,
    /// 管理用户id
    pub admin_id: Option<i32>,
    /// 逻辑删除
    pub deleted: bool,
    /// 公司名称
    pub commpany: Option<String>,
    /// 自动监控更新商品
    pub auto_update_good: Option<bool>,
    /// 店铺url地址
    pub shop_url: Option<String>,
    /// 默认的商品类别id
    pub default_category_id: Option<i32>,
    /// 默认商品页面数
    pub default_pages: Option<i32>,
    /// 店铺商品溢价
    pub add_precent: Option<i32>,
    /// 提货地址
    pub address: Option<String>,
    /// 经度
    pub longitude: Option<f64>,
    /// 纬度
    pub latitude: Option<f64>,
    /// 提货时间配置
    pub fetch_time_rules: Option<String>,
}

impl BaseEntity for Brand {
    fn id(&self) -> i32 {
        self.id
    }

    fn add_time(&self) -> Option<DateTime<Utc>> {
        self.add_time
    }

    fn update_time(&self) -> Option<DateTime<Utc>> {
        self.update_time
    }

    fn is_deleted(&self) -> bool {
        self.deleted
    }
}

/// 商品详情（包含规格、属性等）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GoodsDetail {
    pub goods: Goods,
    pub specifications: Vec<GoodsSpecification>,
    pub products: Vec<GoodsProduct>,
    pub attributes: Vec<GoodsAttribute>,
}

impl GoodsDetail {
    /// 根据产品ID查找产品
    pub fn find_product(&self, product_id: i32) -> Option<&GoodsProduct> {
        self.products.iter().find(|p| p.id == product_id)
    }

    /// 获取最低价格
    pub fn min_price(&self) -> Option<&Decimal> {
        self.products.iter().map(|p| &p.price).min()
    }

    /// 获取最高价格
    pub fn max_price(&self) -> Option<&Decimal> {
        self.products.iter().map(|p| &p.price).max()
    }

    /// 获取总库存
    pub fn total_stock(&self) -> i32 {
        self.products.iter().map(|p| p.number).sum()
    }
}

/// 商品查询参数
#[derive(Debug, Clone, Deserialize)]
pub struct GoodsQuery {
    #[serde(flatten)]
    pub page: PageQuery,
    /// 商品名称模糊查询
    pub name: Option<String>,
    /// 商品编号
    pub goods_sn: Option<String>,
    /// 分类ID
    pub category_id: Option<i32>,
    /// 品牌ID
    pub brand_id: Option<i32>,
    /// 是否上架
    pub is_on_sale: Option<bool>,
    /// 是否新品
    pub is_new: Option<bool>,
    /// 是否热门
    pub is_hot: Option<bool>,
    /// 审批状态
    pub approve_status: Option<ApproveStatus>,
    /// 最低价格
    pub min_price: Option<Decimal>,
    /// 最高价格
    pub max_price: Option<Decimal>,
    /// 排序字段
    pub sort_by: Option<String>,
    /// 排序方向
    pub sort_order: Option<String>,
}

/// 商品搜索参数
#[derive(Debug, Clone, Deserialize)]
pub struct SearchQuery {
    #[serde(flatten)]
    pub page: PageQuery,
    /// 搜索关键字
    pub keyword: Option<String>,
    /// 分类ID
    pub category_id: Option<i32>,
    /// 最低价格
    pub min_price: Option<Decimal>,
    /// 最高价格
    pub max_price: Option<Decimal>,
    /// 排序字段：price, sales, add_time
    pub sort_by: Option<String>,
    /// 排序方向：asc, desc
    pub sort_order: Option<String>,
    /// 用户ID（用于记录搜索历史）
    pub user_id: Option<i32>,
}

/// 商品搜索结果
#[derive(Debug, Clone, Serialize)]
pub struct SearchResult {
    pub goods: Vec<Goods>,
    pub total: u64,
    pub aggregations: SearchAggregations,
}

/// 搜索聚合信息
#[derive(Debug, Clone, Serialize)]
pub struct SearchAggregations {
    /// 价格区间统计
    pub price_ranges: Vec<PriceRange>,
    /// 分类统计
    pub categories: Vec<CategoryCount>,
    /// 品牌统计
    pub brands: Vec<BrandCount>,
}

/// 价格区间
#[derive(Debug, Clone, Serialize)]
pub struct PriceRange {
    pub min: Decimal,
    pub max: Decimal,
    pub count: u64,
}

/// 分类统计
#[derive(Debug, Clone, Serialize)]
pub struct CategoryCount {
    pub category_id: i32,
    pub category_name: String,
    pub count: u64,
}

/// 品牌统计
#[derive(Debug, Clone, Serialize)]
pub struct BrandCount {
    pub brand_id: i32,
    pub brand_name: String,
    pub count: u64,
}

/// 分类树结构
#[derive(Debug, Clone, Serialize)]
pub struct CategoryTree {
    pub id: i32,
    pub name: String,
    pub icon_url: Option<String>,
    pub children: Vec<CategoryTree>,
}
