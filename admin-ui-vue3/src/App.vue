<template>
  <div class="min-h-screen bg-background">
    <!-- 简单导航栏 -->
    <nav class="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div class="container mx-auto px-4">
        <div class="flex h-14 items-center justify-between">
          <div class="flex items-center space-x-6">
            <router-link to="/" class="font-bold text-lg">DTS Shop Admin</router-link>
            <div class="flex items-center space-x-4">
              <router-link
                to="/dashboard"
                class="text-sm font-medium transition-colors hover:text-primary"
                active-class="text-primary"
              >
                首页
              </router-link>
              <router-link
                to="/showcase"
                class="text-sm font-medium transition-colors hover:text-primary"
                active-class="text-primary"
              >
                组件展示
              </router-link>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <LangSelect />
            <ThemeToggle />
          </div>
        </div>
      </div>
    </nav>

    <!-- 主内容 -->
    <main class="container mx-auto px-4 py-6">
      <router-view />
    </main>

    <!-- 返回顶部 -->
    <BackToTop />
  </div>
</template>

<script setup lang="ts">
import LangSelect from '@/components/LangSelect/index.vue'
import ThemeToggle from '@/components/ThemeToggle/index.vue'
import BackToTop from '@/components/BackToTop/index.vue'
</script>

<style>
/* 可引入全局样式 */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}
</style>
