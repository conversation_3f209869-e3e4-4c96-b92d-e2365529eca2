/* 响应式工具样式 */

/* 容器最大宽度 */
.container-responsive {
  @apply w-full mx-auto px-4;
  @apply sm:px-6;
  @apply lg:px-8;
  @apply max-w-7xl;
}

/* 响应式网格系统 */
.grid-responsive-1 {
  @apply grid grid-cols-1 gap-4;
}

.grid-responsive-2 {
  @apply grid gap-4;
  @apply grid-cols-1;
  @apply sm:grid-cols-2;
}

.grid-responsive-3 {
  @apply grid gap-4;
  @apply grid-cols-1;
  @apply sm:grid-cols-2;
  @apply lg:grid-cols-3;
}

.grid-responsive-4 {
  @apply grid gap-4;
  @apply grid-cols-1;
  @apply sm:grid-cols-2;
  @apply lg:grid-cols-3;
  @apply xl:grid-cols-4;
}

.grid-responsive-6 {
  @apply grid gap-4;
  @apply grid-cols-2;
  @apply sm:grid-cols-3;
  @apply md:grid-cols-4;
  @apply lg:grid-cols-5;
  @apply xl:grid-cols-6;
}

/* 响应式弹性布局 */
.flex-responsive-col {
  @apply flex flex-col;
  @apply md:flex-row;
}

.flex-responsive-row {
  @apply flex flex-row;
  @apply md:flex-col;
}

.flex-responsive-wrap {
  @apply flex flex-wrap;
  @apply gap-2;
  @apply sm:gap-4;
}

/* 响应式间距 */
.spacing-responsive-sm {
  @apply p-2;
  @apply sm:p-3;
  @apply md:p-4;
}

.spacing-responsive-md {
  @apply p-3;
  @apply sm:p-4;
  @apply md:p-6;
  @apply lg:p-8;
}

.spacing-responsive-lg {
  @apply p-4;
  @apply sm:p-6;
  @apply md:p-8;
  @apply lg:p-12;
}

/* 响应式边距 */
.margin-responsive-sm {
  @apply m-2;
  @apply sm:m-3;
  @apply md:m-4;
}

.margin-responsive-md {
  @apply m-3;
  @apply sm:m-4;
  @apply md:m-6;
  @apply lg:m-8;
}

/* 响应式文字大小 */
.text-responsive-sm {
  @apply text-xs;
  @apply sm:text-sm;
  @apply md:text-base;
}

.text-responsive-md {
  @apply text-sm;
  @apply sm:text-base;
  @apply md:text-lg;
}

.text-responsive-lg {
  @apply text-base;
  @apply sm:text-lg;
  @apply md:text-xl;
  @apply lg:text-2xl;
}

.text-responsive-xl {
  @apply text-lg;
  @apply sm:text-xl;
  @apply md:text-2xl;
  @apply lg:text-3xl;
  @apply xl:text-4xl;
}

/* 响应式标题 */
.heading-responsive-h1 {
  @apply text-2xl font-bold;
  @apply sm:text-3xl;
  @apply md:text-4xl;
  @apply lg:text-5xl;
}

.heading-responsive-h2 {
  @apply text-xl font-semibold;
  @apply sm:text-2xl;
  @apply md:text-3xl;
  @apply lg:text-4xl;
}

.heading-responsive-h3 {
  @apply text-lg font-medium;
  @apply sm:text-xl;
  @apply md:text-2xl;
  @apply lg:text-3xl;
}

/* 响应式按钮组 */
.button-group-responsive {
  @apply flex gap-2;
  @apply flex-col;
  @apply sm:flex-row;
  @apply sm:items-center;
}

.button-group-responsive-center {
  @apply flex gap-2;
  @apply flex-col items-center;
  @apply sm:flex-row;
  @apply sm:justify-center;
}

/* 响应式卡片 */
.card-responsive {
  @apply w-full;
  @apply max-w-sm;
  @apply sm:max-w-md;
  @apply md:max-w-lg;
  @apply lg:max-w-xl;
  @apply mx-auto;
}

.card-responsive-full {
  @apply w-full;
  @apply max-w-full;
  @apply sm:max-w-2xl;
  @apply md:max-w-4xl;
  @apply lg:max-w-6xl;
  @apply mx-auto;
}

/* 响应式表格 */
.table-responsive {
  @apply w-full overflow-x-auto;
}

.table-responsive table {
  @apply min-w-full;
}

.table-responsive th,
.table-responsive td {
  @apply px-2 py-1;
  @apply sm:px-4 sm:py-2;
  @apply md:px-6 md:py-3;
}

/* 响应式图片 */
.image-responsive {
  @apply w-full h-auto;
  @apply max-w-xs;
  @apply sm:max-w-sm;
  @apply md:max-w-md;
  @apply lg:max-w-lg;
}

.image-responsive-square {
  @apply w-16 h-16;
  @apply sm:w-20 sm:h-20;
  @apply md:w-24 md:h-24;
  @apply lg:w-32 lg:h-32;
}

/* 响应式隐藏/显示 */
.hide-mobile {
  @apply hidden;
  @apply md:block;
}

.hide-desktop {
  @apply block;
  @apply md:hidden;
}

.show-mobile-only {
  @apply block;
  @apply sm:hidden;
}

.show-tablet-only {
  @apply hidden;
  @apply sm:block;
  @apply lg:hidden;
}

.show-desktop-only {
  @apply hidden;
  @apply lg:block;
}

/* 响应式侧边栏 */
.sidebar-responsive {
  @apply fixed inset-y-0 left-0 z-50;
  @apply w-64;
  @apply transform transition-transform duration-300 ease-in-out;
  @apply -translate-x-full;
  @apply lg:translate-x-0 lg:static lg:inset-0;
}

.sidebar-responsive.open {
  @apply translate-x-0;
}

/* 响应式导航 */
.nav-responsive {
  @apply flex flex-col;
  @apply lg:flex-row;
  @apply space-y-2;
  @apply lg:space-y-0 lg:space-x-4;
}

/* 响应式表单 */
.form-responsive {
  @apply space-y-4;
  @apply sm:space-y-6;
}

.form-group-responsive {
  @apply flex flex-col;
  @apply sm:flex-row;
  @apply sm:items-center;
  @apply gap-2;
  @apply sm:gap-4;
}

.form-input-responsive {
  @apply w-full;
  @apply sm:max-w-xs;
  @apply md:max-w-sm;
}

/* 响应式模态框 */
.modal-responsive {
  @apply w-full max-w-sm;
  @apply sm:max-w-md;
  @apply md:max-w-lg;
  @apply lg:max-w-xl;
  @apply mx-4;
  @apply sm:mx-auto;
}

/* 响应式工具栏 */
.toolbar-responsive {
  @apply flex flex-col;
  @apply sm:flex-row;
  @apply sm:items-center;
  @apply sm:justify-between;
  @apply gap-2;
  @apply sm:gap-4;
}

/* 响应式面包屑 */
.breadcrumb-responsive {
  @apply text-sm;
  @apply sm:text-base;
  @apply truncate;
  @apply sm:overflow-visible;
}

/* 响应式分页 */
.pagination-responsive {
  @apply flex flex-col;
  @apply sm:flex-row;
  @apply sm:items-center;
  @apply sm:justify-between;
  @apply gap-4;
}

/* 响应式统计卡片 */
.stats-responsive {
  @apply grid gap-4;
  @apply grid-cols-1;
  @apply sm:grid-cols-2;
  @apply lg:grid-cols-4;
}

.stat-card-responsive {
  @apply p-4;
  @apply sm:p-6;
  @apply text-center;
  @apply sm:text-left;
}
