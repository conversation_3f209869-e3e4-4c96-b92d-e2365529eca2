import { ref, computed, onMounted, onUnmounted } from 'vue'

// 断点定义（与Tailwind CSS保持一致）
export const BREAKPOINTS = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const

export type Breakpoint = keyof typeof BREAKPOINTS

// 全局响应式状态
const windowWidth = ref(0)
const windowHeight = ref(0)

export function useResponsive() {
  // 更新窗口尺寸
  const updateWindowSize = () => {
    windowWidth.value = window.innerWidth
    windowHeight.value = window.innerHeight
  }

  // 检查是否匹配指定断点
  const isBreakpoint = (breakpoint: Breakpoint) => {
    return computed(() => windowWidth.value >= BREAKPOINTS[breakpoint])
  }

  // 检查是否在指定断点范围内
  const isBetween = (min: Breakpoint, max: Breakpoint) => {
    return computed(() => 
      windowWidth.value >= BREAKPOINTS[min] && 
      windowWidth.value < BREAKPOINTS[max]
    )
  }

  // 获取当前断点
  const currentBreakpoint = computed<Breakpoint>(() => {
    const width = windowWidth.value
    if (width >= BREAKPOINTS['2xl']) return '2xl'
    if (width >= BREAKPOINTS.xl) return 'xl'
    if (width >= BREAKPOINTS.lg) return 'lg'
    if (width >= BREAKPOINTS.md) return 'md'
    if (width >= BREAKPOINTS.sm) return 'sm'
    return 'sm' // 默认为最小断点
  })

  // 常用断点检查
  const isMobile = computed(() => windowWidth.value < BREAKPOINTS.md)
  const isTablet = computed(() => 
    windowWidth.value >= BREAKPOINTS.md && windowWidth.value < BREAKPOINTS.lg
  )
  const isDesktop = computed(() => windowWidth.value >= BREAKPOINTS.lg)
  const isLargeScreen = computed(() => windowWidth.value >= BREAKPOINTS.xl)

  // 屏幕方向
  const isLandscape = computed(() => windowWidth.value > windowHeight.value)
  const isPortrait = computed(() => windowHeight.value > windowWidth.value)

  // 获取响应式列数（用于网格布局）
  const getResponsiveColumns = (config: Partial<Record<Breakpoint, number>>) => {
    return computed(() => {
      const breakpoint = currentBreakpoint.value
      
      // 从当前断点开始向下查找配置
      const breakpointOrder: Breakpoint[] = ['2xl', 'xl', 'lg', 'md', 'sm']
      const currentIndex = breakpointOrder.indexOf(breakpoint)
      
      for (let i = currentIndex; i < breakpointOrder.length; i++) {
        const bp = breakpointOrder[i]
        if (config[bp] !== undefined) {
          return config[bp]!
        }
      }
      
      // 如果没有找到配置，返回默认值
      return config.sm || 1
    })
  }

  // 获取响应式间距
  const getResponsiveSpacing = (config: Partial<Record<Breakpoint, string>>) => {
    return computed(() => {
      const breakpoint = currentBreakpoint.value
      return config[breakpoint] || config.sm || '1rem'
    })
  }

  // 初始化
  const init = () => {
    updateWindowSize()
    window.addEventListener('resize', updateWindowSize)
    
    return () => {
      window.removeEventListener('resize', updateWindowSize)
    }
  }

  return {
    // 状态
    windowWidth: computed(() => windowWidth.value),
    windowHeight: computed(() => windowHeight.value),
    currentBreakpoint,
    
    // 断点检查
    isBreakpoint,
    isBetween,
    isMobile,
    isTablet,
    isDesktop,
    isLargeScreen,
    
    // 屏幕方向
    isLandscape,
    isPortrait,
    
    // 工具函数
    getResponsiveColumns,
    getResponsiveSpacing,
    
    // 初始化
    init,
  }
}

// 全局响应式实例
export const globalResponsive = useResponsive()

// 初始化全局响应式
let cleanup: (() => void) | null = null

export const initializeResponsive = () => {
  if (cleanup) cleanup()
  cleanup = globalResponsive.init()
}

export const cleanupResponsive = () => {
  if (cleanup) {
    cleanup()
    cleanup = null
  }
}

// 响应式断点匹配的媒体查询hook
export function useMediaQuery(query: string) {
  const matches = ref(false)
  let mediaQuery: MediaQueryList | null = null

  const updateMatches = (e: MediaQueryListEvent | MediaQueryList) => {
    matches.value = e.matches
  }

  onMounted(() => {
    mediaQuery = window.matchMedia(query)
    updateMatches(mediaQuery)
    mediaQuery.addEventListener('change', updateMatches)
  })

  onUnmounted(() => {
    if (mediaQuery) {
      mediaQuery.removeEventListener('change', updateMatches)
    }
  })

  return matches
}

// 预定义的媒体查询
export const useBreakpoints = () => {
  return {
    isSm: useMediaQuery(`(min-width: ${BREAKPOINTS.sm}px)`),
    isMd: useMediaQuery(`(min-width: ${BREAKPOINTS.md}px)`),
    isLg: useMediaQuery(`(min-width: ${BREAKPOINTS.lg}px)`),
    isXl: useMediaQuery(`(min-width: ${BREAKPOINTS.xl}px)`),
    is2Xl: useMediaQuery(`(min-width: ${BREAKPOINTS['2xl']}px)`),
    isMobile: useMediaQuery(`(max-width: ${BREAKPOINTS.md - 1}px)`),
    isTablet: useMediaQuery(`(min-width: ${BREAKPOINTS.md}px) and (max-width: ${BREAKPOINTS.lg - 1}px)`),
    isDesktop: useMediaQuery(`(min-width: ${BREAKPOINTS.lg}px)`),
  }
}
