import { ref, computed, watch, onMounted } from 'vue'

export type Theme = 'light' | 'dark' | 'system'
export type ColorScheme = 'light' | 'dark'

const STORAGE_KEY = 'theme-preference'
const MEDIA_QUERY = '(prefers-color-scheme: dark)'

// 全局状态
const theme = ref<Theme>('system')
const systemPreference = ref<ColorScheme>('light')

// 计算当前实际的颜色方案
const colorScheme = computed<ColorScheme>(() => {
  if (theme.value === 'system') {
    return systemPreference.value
  }
  return theme.value as ColorScheme
})

// 预设主题颜色
export const PRESET_COLORS = [
  { name: '默认蓝', value: '#3b82f6', hsl: '217 91% 60%' },
  { name: '石墨', value: '#64748b', hsl: '215 16% 47%' },
  { name: '红色', value: '#ef4444', hsl: '0 84% 60%' },
  { name: '橙色', value: '#f97316', hsl: '21 90% 52%' },
  { name: '琥珀', value: '#f59e0b', hsl: '45 93% 47%' },
  { name: '黄色', value: '#eab308', hsl: '54 91% 47%' },
  { name: '青柠', value: '#84cc16', hsl: '84 81% 44%' },
  { name: '绿色', value: '#22c55e', hsl: '142 71% 45%' },
  { name: '翠绿', value: '#10b981', hsl: '158 64% 52%' },
  { name: '青色', value: '#06b6d4', hsl: '188 94% 42%' },
  { name: '天蓝', value: '#0ea5e9', hsl: '199 89% 48%' },
  { name: '靛蓝', value: '#6366f1', hsl: '239 84% 67%' },
  { name: '紫色', value: '#8b5cf6', hsl: '262 83% 58%' },
  { name: '紫红', value: '#a855f7', hsl: '271 81% 56%' },
  { name: '粉色', value: '#ec4899', hsl: '322 81% 57%' },
  { name: '玫瑰', value: '#f43f5e', hsl: '351 83% 61%' },
] as const

export function useTheme() {
  // 设置主题
  const setTheme = (newTheme: Theme) => {
    theme.value = newTheme
    localStorage.setItem(STORAGE_KEY, newTheme)
    applyTheme()
  }

  // 切换主题
  const toggleTheme = () => {
    const themes: Theme[] = ['light', 'dark', 'system']
    const currentIndex = themes.indexOf(theme.value)
    const nextIndex = (currentIndex + 1) % themes.length
    setTheme(themes[nextIndex])
  }

  // 应用主题到DOM
  const applyTheme = () => {
    const root = document.documentElement
    const isDark = colorScheme.value === 'dark'
    
    root.classList.toggle('dark', isDark)
    
    // 更新meta标签以支持移动端状态栏
    updateMetaThemeColor(isDark)
  }

  // 更新meta主题颜色
  const updateMetaThemeColor = (isDark: boolean) => {
    const metaThemeColor = document.querySelector('meta[name="theme-color"]')
    const color = isDark ? '#0f0f0f' : '#ffffff'
    
    if (metaThemeColor) {
      metaThemeColor.setAttribute('content', color)
    } else {
      const meta = document.createElement('meta')
      meta.name = 'theme-color'
      meta.content = color
      document.head.appendChild(meta)
    }
  }

  // 设置自定义主题颜色
  const setThemeColor = (color: string) => {
    const root = document.documentElement
    const hsl = hexToHsl(color)
    
    // 更新主色调
    root.style.setProperty('--primary', `${hsl.h} ${hsl.s}% ${hsl.l}%`)
    
    // 生成相关颜色变体
    const variants = generateColorVariants(hsl)
    Object.entries(variants).forEach(([key, value]) => {
      root.style.setProperty(`--${key}`, value)
    })
    
    // 保存到localStorage
    localStorage.setItem('theme-color', color)
  }

  // 重置主题颜色
  const resetThemeColor = () => {
    const root = document.documentElement
    const defaultColor = PRESET_COLORS[0]
    
    setThemeColor(defaultColor.value)
    localStorage.removeItem('theme-color')
  }

  // 获取当前主题颜色
  const getCurrentThemeColor = (): string => {
    return localStorage.getItem('theme-color') || PRESET_COLORS[0].value
  }

  // 监听系统主题变化
  const watchSystemTheme = () => {
    const mediaQuery = window.matchMedia(MEDIA_QUERY)
    
    const updateSystemPreference = (e: MediaQueryListEvent | MediaQueryList) => {
      systemPreference.value = e.matches ? 'dark' : 'light'
    }
    
    updateSystemPreference(mediaQuery)
    mediaQuery.addEventListener('change', updateSystemPreference)
    
    return () => {
      mediaQuery.removeEventListener('change', updateSystemPreference)
    }
  }

  // 初始化主题
  const initTheme = () => {
    // 从localStorage恢复主题设置
    const savedTheme = localStorage.getItem(STORAGE_KEY) as Theme
    if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {
      theme.value = savedTheme
    }
    
    // 恢复自定义颜色
    const savedColor = localStorage.getItem('theme-color')
    if (savedColor) {
      setThemeColor(savedColor)
    }
    
    // 监听系统主题变化
    const cleanup = watchSystemTheme()
    
    // 应用初始主题
    applyTheme()
    
    return cleanup
  }

  // 监听主题变化
  watch(colorScheme, applyTheme)

  return {
    theme: computed(() => theme.value),
    colorScheme,
    setTheme,
    toggleTheme,
    setThemeColor,
    resetThemeColor,
    getCurrentThemeColor,
    initTheme,
    presetColors: PRESET_COLORS,
  }
}

// 工具函数：将hex颜色转换为HSL
function hexToHsl(hex: string): { h: number; s: number; l: number } {
  const r = parseInt(hex.slice(1, 3), 16) / 255
  const g = parseInt(hex.slice(3, 5), 16) / 255
  const b = parseInt(hex.slice(5, 7), 16) / 255

  const max = Math.max(r, g, b)
  const min = Math.min(r, g, b)
  let h = 0, s = 0, l = (max + min) / 2

  if (max !== min) {
    const d = max - min
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min)
    
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break
      case g: h = (b - r) / d + 2; break
      case b: h = (r - g) / d + 4; break
    }
    h /= 6
  }

  return {
    h: Math.round(h * 360),
    s: Math.round(s * 100),
    l: Math.round(l * 100)
  }
}

// 工具函数：生成颜色变体
function generateColorVariants(hsl: { h: number; s: number; l: number }) {
  return {
    'primary-foreground': `${hsl.h} ${hsl.s}% ${hsl.l > 50 ? 10 : 90}%`,
    'ring': `${hsl.h} ${Math.round(hsl.s * 0.8)}% ${Math.min(hsl.l + 10, 80)}%`,
  }
}

// 全局主题实例
export const globalTheme = useTheme()

// 在应用启动时初始化主题
let cleanup: (() => void) | null = null

export const initializeTheme = () => {
  if (cleanup) cleanup()
  cleanup = globalTheme.initTheme()
}

// 清理函数
export const cleanupTheme = () => {
  if (cleanup) {
    cleanup()
    cleanup = null
  }
}
