<template>
  <div class="container-responsive">
    <div class="spacing-responsive-lg">
      <!-- 页面标题 -->
      <div class="text-center mb-8">
        <h1 class="heading-responsive-h1 mb-4">响应式设计演示</h1>
        <p class="text-responsive-md text-muted-foreground">
          展示各种响应式组件和布局在不同屏幕尺寸下的表现
        </p>
      </div>

      <!-- 当前断点信息 -->
      <Card class="mb-8">
        <CardContent class="spacing-responsive-md">
          <h2 class="heading-responsive-h3 mb-4">当前屏幕信息</h2>
          <div class="grid-responsive-2">
            <div>
              <p class="text-sm text-muted-foreground">屏幕宽度</p>
              <p class="text-lg font-semibold">{{ windowWidth }}px</p>
            </div>
            <div>
              <p class="text-sm text-muted-foreground">当前断点</p>
              <Badge variant="outline" class="text-sm">{{ currentBreakpoint }}</Badge>
            </div>
            <div>
              <p class="text-sm text-muted-foreground">设备类型</p>
              <p class="text-lg font-semibold">
                {{ isMobile ? '移动端' : isTablet ? '平板' : '桌面端' }}
              </p>
            </div>
            <div>
              <p class="text-sm text-muted-foreground">屏幕方向</p>
              <p class="text-lg font-semibold">
                {{ isLandscape ? '横屏' : '竖屏' }}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 响应式网格演示 -->
      <Card class="mb-8">
        <CardContent class="spacing-responsive-md">
          <h2 class="heading-responsive-h3 mb-4">响应式网格布局</h2>
          <div class="space-y-6">
            <div>
              <h3 class="text-lg font-medium mb-2">2列网格 (移动端1列，桌面端2列)</h3>
              <div class="grid-responsive-2">
                <Card v-for="i in 4" :key="i" class="p-4 text-center">
                  <p>网格项 {{ i }}</p>
                </Card>
              </div>
            </div>
            
            <div>
              <h3 class="text-lg font-medium mb-2">4列网格 (响应式调整)</h3>
              <div class="grid-responsive-4">
                <Card v-for="i in 8" :key="i" class="p-4 text-center">
                  <p>项目 {{ i }}</p>
                </Card>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 响应式组件演示 -->
      <Card class="mb-8">
        <CardContent class="spacing-responsive-md">
          <h2 class="heading-responsive-h3 mb-4">响应式组件</h2>
          <div class="space-y-6">
            <!-- 按钮组 -->
            <div>
              <h3 class="text-lg font-medium mb-2">响应式按钮组</h3>
              <div class="button-group-responsive">
                <Button>主要按钮</Button>
                <Button variant="outline">次要按钮</Button>
                <Button variant="ghost">文本按钮</Button>
                <Button variant="destructive">危险按钮</Button>
              </div>
            </div>

            <!-- 表单 -->
            <div>
              <h3 class="text-lg font-medium mb-2">响应式表单</h3>
              <form class="form-responsive">
                <div class="form-group-responsive">
                  <label class="text-sm font-medium min-w-20">姓名:</label>
                  <Input placeholder="请输入姓名" class="form-input-responsive" />
                </div>
                <div class="form-group-responsive">
                  <label class="text-sm font-medium min-w-20">邮箱:</label>
                  <Input type="email" placeholder="请输入邮箱" class="form-input-responsive" />
                </div>
                <div class="form-group-responsive">
                  <label class="text-sm font-medium min-w-20">类型:</label>
                  <Select>
                    <SelectTrigger class="form-input-responsive">
                      <SelectValue placeholder="选择类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="type1">类型1</SelectItem>
                      <SelectItem value="type2">类型2</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </form>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 响应式表格演示 -->
      <Card class="mb-8">
        <CardContent class="spacing-responsive-md">
          <h2 class="heading-responsive-h3 mb-4">响应式表格</h2>
          <div class="table-responsive">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>ID</TableHead>
                  <TableHead>名称</TableHead>
                  <TableHead class="hide-mobile">邮箱</TableHead>
                  <TableHead class="hide-mobile">创建时间</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow v-for="i in 5" :key="i">
                  <TableCell>{{ i }}</TableCell>
                  <TableCell>用户{{ i }}</TableCell>
                  <TableCell class="hide-mobile">user{{ i }}@example.com</TableCell>
                  <TableCell class="hide-mobile">2024-01-{{ String(i).padStart(2, '0') }}</TableCell>
                  <TableCell>
                    <div class="flex gap-1">
                      <Button size="sm" variant="outline">编辑</Button>
                      <Button size="sm" variant="destructive" class="hide-mobile">删除</Button>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      <!-- 分页演示 -->
      <Card class="mb-8">
        <CardContent class="spacing-responsive-md">
          <h2 class="heading-responsive-h3 mb-4">响应式分页</h2>
          <Pagination
            :total="100"
            :page="currentPage"
            :limit="pageSize"
            @pagination="handlePagination"
          />
        </CardContent>
      </Card>

      <!-- 二维码管理器演示 -->
      <Card class="mb-8">
        <CardContent class="spacing-responsive-md">
          <h2 class="heading-responsive-h3 mb-4">响应式二维码管理器</h2>
          <QrcodeManager
            type="DEMO"
            :related-id="123"
            scene="demo"
          />
        </CardContent>
      </Card>

      <!-- 主题切换演示 -->
      <Card>
        <CardContent class="spacing-responsive-md">
          <h2 class="heading-responsive-h3 mb-4">主题系统</h2>
          <div class="flex-responsive-col gap-4">
            <div>
              <h3 class="text-lg font-medium mb-2">主题切换</h3>
              <div class="flex items-center gap-4">
                <ThemeToggle />
                <ThemePicker />
              </div>
            </div>
            <div>
              <h3 class="text-lg font-medium mb-2">语言和尺寸</h3>
              <div class="flex items-center gap-4">
                <LangSelect />
                <SizeSelect />
                <Screenfull />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useResponsive } from '@/composables/useResponsive'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import Pagination from '@/components/Pagination/index.vue'
import QrcodeManager from '@/components/QrcodeManager/index.vue'
import ThemeToggle from '@/components/ThemeToggle/index.vue'
import ThemePicker from '@/components/ThemePicker/index.vue'
import LangSelect from '@/components/LangSelect/index.vue'
import SizeSelect from '@/components/SizeSelect/index.vue'
import Screenfull from '@/components/Screenfull/index.vue'

const {
  windowWidth,
  currentBreakpoint,
  isMobile,
  isTablet,
  isLandscape,
} = useResponsive()

const currentPage = ref(1)
const pageSize = ref(10)

const handlePagination = (data: { page: number; limit: number }) => {
  currentPage.value = data.page
  pageSize.value = data.limit
}
</script>

<style scoped>
/* 组件特定的响应式样式 */
</style>
