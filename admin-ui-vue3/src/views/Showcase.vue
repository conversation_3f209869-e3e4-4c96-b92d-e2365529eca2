<template>
  <div class="showcase-container">
    <!-- 页面头部 -->
    <div class="showcase-header">
      <h1 class="text-3xl font-bold mb-2">组件展示页面</h1>
      <p class="text-muted-foreground mb-6">
        展示所有迁移的UI组件，方便调试和测试
      </p>
      
      <!-- 快速导航 -->
      <div class="flex flex-wrap gap-2 mb-8">
        <Button
          v-for="section in sections"
          :key="section.id"
          variant="outline"
          size="sm"
          @click="scrollToSection(section.id)"
        >
          {{ section.name }}
        </Button>
      </div>
    </div>

    <!-- 基础布局组件 -->
    <section id="layout" class="showcase-section">
      <h2 class="section-title">基础布局组件</h2>
      
      <!-- BackToTop -->
      <div class="component-demo">
        <h3 class="component-title">BackToTop - 返回顶部</h3>
        <div class="demo-area">
          <p class="text-sm text-muted-foreground mb-4">
            滚动页面到底部可以看到返回顶部按钮
          </p>
          <BackToTop />
        </div>
      </div>

      <!-- 简单演示 -->
      <div class="component-demo">
        <h3 class="component-title">基础布局组件演示</h3>
        <div class="demo-area">
          <p class="text-sm text-muted-foreground">
            基础布局组件包括返回顶部按钮和汉堡菜单等。
          </p>
        </div>
      </div>

      <!-- Hamburger -->
      <div class="component-demo">
        <h3 class="component-title">Hamburger - 汉堡菜单</h3>
        <div class="demo-area">
          <div class="flex items-center gap-4">
            <Hamburger :is-active="hamburgerActive" @toggle="hamburgerActive = !hamburgerActive" />
            <span class="text-sm">状态: {{ hamburgerActive ? '激活' : '未激活' }}</span>
          </div>
        </div>
      </div>
    </section>

    <!-- 导航组件 -->
    <section id="navigation" class="showcase-section">
      <h2 class="section-title">导航组件</h2>
      
      <!-- LangSelect -->
      <div class="component-demo">
        <h3 class="component-title">LangSelect - 语言选择</h3>
        <div class="demo-area">
          <LangSelect />
        </div>
      </div>

      <!-- SizeSelect -->
      <div class="component-demo">
        <h3 class="component-title">SizeSelect - 尺寸选择</h3>
        <div class="demo-area">
          <SizeSelect />
        </div>
      </div>

      <!-- Screenfull -->
      <div class="component-demo">
        <h3 class="component-title">Screenfull - 全屏切换</h3>
        <div class="demo-area">
          <Screenfull />
        </div>
      </div>
    </section>

    <!-- 功能组件 -->
    <section id="functional" class="showcase-section">
      <h2 class="section-title">功能组件</h2>
      
      <!-- 功能组件演示 -->
      <div class="component-demo">
        <h3 class="component-title">功能组件演示</h3>
        <div class="demo-area">
          <p class="text-sm text-muted-foreground mb-4">
            功能组件包括滚动面板、SVG图标等实用工具。
          </p>
          <div class="flex flex-wrap gap-2">
            <div v-for="i in 10" :key="i" class="px-3 py-1 bg-primary text-primary-foreground rounded-md text-sm">
              标签 {{ i }}
            </div>
          </div>
        </div>
      </div>

      <!-- ThemePicker -->
      <div class="component-demo">
        <h3 class="component-title">ThemePicker - 主题选择器</h3>
        <div class="demo-area">
          <ThemePicker />
        </div>
      </div>
    </section>

    <!-- 主题系统 -->
    <section id="theme" class="showcase-section">
      <h2 class="section-title">主题系统</h2>
      
      <!-- ThemeToggle -->
      <div class="component-demo">
        <h3 class="component-title">ThemeToggle - 主题切换</h3>
        <div class="demo-area">
          <div class="flex items-center gap-4">
            <ThemeToggle />
            <div class="text-sm">
              <p>当前主题: <Badge variant="outline">{{ currentTheme }}</Badge></p>
              <p>颜色模式: <Badge variant="outline">{{ colorScheme }}</Badge></p>
            </div>
          </div>
        </div>
      </div>

      <!-- 主题颜色展示 -->
      <div class="component-demo">
        <h3 class="component-title">主题颜色展示</h3>
        <div class="demo-area">
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="p-4 bg-primary text-primary-foreground rounded-lg text-center">
              Primary
            </div>
            <div class="p-4 bg-secondary text-secondary-foreground rounded-lg text-center">
              Secondary
            </div>
            <div class="p-4 bg-muted text-muted-foreground rounded-lg text-center">
              Muted
            </div>
            <div class="p-4 bg-accent text-accent-foreground rounded-lg text-center">
              Accent
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 业务组件 -->
    <section id="business" class="showcase-section">
      <h2 class="section-title">业务组件</h2>
      
      <!-- 业务组件演示 -->
      <div class="component-demo">
        <h3 class="component-title">业务组件演示</h3>
        <div class="demo-area">
          <p class="text-sm text-muted-foreground mb-4">
            业务组件包括分页、二维码管理等功能。
          </p>
          <div class="flex items-center justify-center p-8 border-2 border-dashed border-border rounded-lg">
            <p class="text-muted-foreground">业务组件演示区域</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 响应式演示 -->
    <section id="responsive" class="showcase-section">
      <h2 class="section-title">响应式演示</h2>
      
      <div class="component-demo">
        <h3 class="component-title">屏幕信息</h3>
        <div class="demo-area">
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="text-center">
              <p class="text-sm text-muted-foreground">宽度</p>
              <p class="font-semibold">{{ windowWidth }}px</p>
            </div>
            <div class="text-center">
              <p class="text-sm text-muted-foreground">断点</p>
              <Badge>{{ currentBreakpoint }}</Badge>
            </div>
            <div class="text-center">
              <p class="text-sm text-muted-foreground">设备</p>
              <p class="font-semibold">{{ deviceType }}</p>
            </div>
            <div class="text-center">
              <p class="text-sm text-muted-foreground">方向</p>
              <p class="font-semibold">{{ isLandscape ? '横屏' : '竖屏' }}</p>
            </div>
          </div>
        </div>
      </div>

      <div class="component-demo">
        <h3 class="component-title">响应式网格</h3>
        <div class="demo-area">
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card v-for="i in 8" :key="i" class="p-4 text-center">
              <p>网格项 {{ i }}</p>
            </Card>
          </div>
        </div>
      </div>
    </section>

    <!-- 测试区域 -->
    <section id="testing" class="showcase-section">
      <h2 class="section-title">测试区域</h2>
      
      <div class="component-demo">
        <h3 class="component-title">组合使用示例</h3>
        <div class="demo-area">
          <div class="flex flex-wrap items-center gap-2 p-4 border rounded-lg">
            <LangSelect />
            <SizeSelect />
            <Screenfull />
            <ThemeToggle />
            <ThemePicker />
            <Hamburger :is-active="false" @toggle="() => {}" />
          </div>
        </div>
      </div>

      <div class="component-demo">
        <h3 class="component-title">长内容测试</h3>
        <div class="demo-area">
          <div class="space-y-4">
            <p v-for="i in 20" :key="i" class="text-sm">
              这是第 {{ i }} 段测试内容，用于测试返回顶部按钮的功能。当页面内容足够长时，滚动到底部可以看到返回顶部按钮出现。
            </p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useTheme } from '@/composables/useTheme'
import { useResponsive } from '@/composables/useResponsive'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card } from '@/components/ui/card'

// 导入所有组件
import BackToTop from '@/components/BackToTop/index.vue'
import Hamburger from '@/components/Hamburger/index.vue'
import LangSelect from '@/components/LangSelect/index.vue'
import SizeSelect from '@/components/SizeSelect/index.vue'
import Screenfull from '@/components/Screenfull/index.vue'
import ThemePicker from '@/components/ThemePicker/index.vue'
import ThemeToggle from '@/components/ThemeToggle/index.vue'

// 状态管理
const hamburgerActive = ref(false)
const currentPage = ref(1)
const pageLimit = ref(10)

// 主题和响应式
const { theme: currentTheme, colorScheme } = useTheme()
const { 
  windowWidth, 
  currentBreakpoint, 
  isMobile, 
  isTablet, 
  isDesktop,
  isLandscape 
} = useResponsive()

// 计算设备类型
const deviceType = computed(() => {
  if (isMobile.value) return '移动端'
  if (isTablet.value) return '平板'
  if (isDesktop.value) return '桌面端'
  return '未知'
})

// 导航sections
const sections = [
  { id: 'layout', name: '布局组件' },
  { id: 'navigation', name: '导航组件' },
  { id: 'functional', name: '功能组件' },
  { id: 'theme', name: '主题系统' },
  { id: 'business', name: '业务组件' },
  { id: 'responsive', name: '响应式' },
  { id: 'testing', name: '测试区域' },
]

// 方法
const scrollToSection = (sectionId: string) => {
  const element = document.getElementById(sectionId)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}

const handlePagination = (data: { page: number; limit: number }) => {
  currentPage.value = data.page
  pageLimit.value = data.limit
}
</script>

<style scoped>
.showcase-container {
  max-width: 72rem; /* max-w-6xl */
  margin-left: auto;
  margin-right: auto;
  padding: 1.5rem; /* p-6 */
  display: flex;
  flex-direction: column;
  gap: 3rem; /* space-y-12 */
  min-height: 200vh; /* 确保有足够的内容来测试返回顶部功能 */
}

.showcase-header {
  text-align: center;
}

.showcase-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem; /* space-y-6 */
}

.section-title {
  font-size: 1.5rem; /* text-2xl */
  line-height: 2rem;
  font-weight: 600; /* font-semibold */
  border-bottom: 1px solid var(--color-border);
  padding-bottom: 0.5rem; /* pb-2 */
  margin-bottom: 1.5rem; /* mb-6 */
}

.component-demo {
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: 1.5rem; /* p-6 */
  display: flex;
  flex-direction: column;
  gap: 1rem; /* space-y-4 */
}

.component-title {
  font-size: 1.125rem; /* text-lg */
  line-height: 1.75rem;
  font-weight: 500; /* font-medium */
  color: var(--color-primary);
}

.demo-area {
  display: flex;
  flex-direction: column;
  gap: 1rem; /* space-y-4 */
}
</style>
