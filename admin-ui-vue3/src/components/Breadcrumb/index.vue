<template>
  <Breadcrumb class="app-breadcrumb">
    <TransitionGroup name="breadcrumb">
      <BreadcrumbItem 
        v-for="(item, index) in levelList" 
        v-show="item.meta?.title"
        :key="item.path"
      >
        <BreadcrumbPage 
          v-if="item.redirect === 'noredirect' || index === levelList.length - 1"
          class="no-redirect"
        >
          {{ generateTitle(item.meta.title) }}
        </BreadcrumbPage>
        <BreadcrumbLink 
          v-else
          :to="item.redirect || item.path"
          as="router-link"
        >
          {{ generateTitle(item.meta.title) }}
        </BreadcrumbLink>
        <BreadcrumbSeparator v-if="index < levelList.length - 1" />
      </BreadcrumbItem>
    </TransitionGroup>
  </Breadcrumb>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { 
  Breadcrumb, 
  BreadcrumbItem, 
  BreadcrumbLink, 
  BreadcrumbPage,
  BreadcrumbSeparator 
} from '@/components/ui/breadcrumb'
import { generateTitle } from '@/utils/i18n'

interface RouteItem {
  path: string
  name?: string
  redirect?: string
  meta?: {
    title?: string
  }
}

const route = useRoute()
const levelList = ref<RouteItem[]>([])

const getBreadcrumb = () => {
  const { params } = route
  let matched = route.matched.filter(item => {
    if (item.name) {
      // 处理动态路由参数
      try {
        // 简化的路径参数替换逻辑
        let path = item.path
        Object.keys(params).forEach(key => {
          path = path.replace(`:${key}`, params[key] as string)
        })
        item.path = path
        return true
      } catch (error) {
        console.warn('路径参数替换失败:', error)
        return true
      }
    }
    return false
  }) as RouteItem[]

  const first = matched[0]
  if (first && first.name?.trim().toLowerCase() !== 'dashboard') {
    matched = [{ path: '/dashboard', meta: { title: 'dashboard' } }].concat(matched)
  }
  
  levelList.value = matched
}

watch(() => route.path, getBreadcrumb, { immediate: true })

onMounted(() => {
  getBreadcrumb()
})
</script>

<style scoped>
.app-breadcrumb {
  @apply inline-block text-sm leading-[50px] ml-2.5;
}

.no-redirect {
  @apply text-muted-foreground cursor-text;
}

.breadcrumb-enter-active,
.breadcrumb-leave-active {
  transition: all 0.3s ease;
}

.breadcrumb-enter-from,
.breadcrumb-leave-to {
  opacity: 0;
  transform: translateX(-10px);
}
</style>
