<template>
  <Button
    variant="ghost"
    size="icon"
    class="hamburger"
    :class="{ 'is-active': isActive }"
    @click="handleClick"
  >
    <Menu class="h-5 w-5" />
    <span class="sr-only">切换菜单</span>
  </Button>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button'
import { Menu } from 'lucide-vue-next'

interface Props {
  isActive?: boolean
}

interface Emits {
  (e: 'toggle'): void
}

const props = withDefaults(defineProps<Props>(), {
  isActive: false
})

const emit = defineEmits<Emits>()

const handleClick = () => {
  emit('toggle')
}
</script>

<style scoped>
.hamburger {
  transition: transform 0.3s ease-in-out;
  transform: rotate(90deg);
}

.hamburger.is-active {
  transform: rotate(0deg);
}
</style>
