<template>
  <DropdownMenu>
    <DropdownMenuTrigger as-child>
      <Button variant="ghost" size="icon" class="size-select">
        <Maximize2 class="h-5 w-5" />
        <span class="sr-only">切换尺寸</span>
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent align="end">
      <DropdownMenuItem
        :disabled="size === 'large'"
        @click="handleSetSize('large')"
      >
        <span>Large</span>
      </DropdownMenuItem>
      <DropdownMenuItem
        :disabled="size === 'medium'"
        @click="handleSetSize('medium')"
      >
        <span>Medium</span>
      </DropdownMenuItem>
      <DropdownMenuItem
        :disabled="size === 'small'"
        @click="handleSetSize('small')"
      >
        <span>Small</span>
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
</template>

<script setup lang="ts">
import { computed, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAppStore } from '@/store/app'
import { useTagsViewStore } from '@/store/tagsView'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Maximize2 } from 'lucide-vue-next'
import { toast } from '@/hooks/use-toast'

const route = useRoute()
const router = useRouter()
const appStore = useAppStore()
const tagsViewStore = useTagsViewStore()

const size = computed(() => appStore.size)

const handleSetSize = async (newSize: string) => {
  appStore.setSize(newSize)
  await refreshView()
  
  toast({
    title: "尺寸切换成功",
    description: `已切换到${newSize}尺寸`,
  })
}

const refreshView = async () => {
  // 清除缓存的视图以重新渲染
  tagsViewStore.delAllCachedViews()
  
  const { fullPath } = route
  
  await nextTick()
  router.replace({
    path: '/redirect' + fullPath
  })
}
</script>


