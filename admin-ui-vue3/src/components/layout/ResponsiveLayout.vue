<template>
  <div class="responsive-layout">
    <!-- 移动端头部 -->
    <header v-if="isMobile" class="mobile-header">
      <div class="flex items-center justify-between p-4">
        <div class="flex items-center space-x-3">
          <Hamburger :is-active="sidebarOpen" @toggle="toggleSidebar" />
          <h1 class="text-lg font-semibold">{{ title }}</h1>
        </div>
        <div class="flex items-center space-x-2">
          <ThemeToggle />
          <LangSelect />
        </div>
      </div>
    </header>

    <div class="layout-container">
      <!-- 侧边栏 -->
      <aside 
        :class="[
          'sidebar',
          {
            'sidebar-open': sidebarOpen,
            'sidebar-closed': !sidebarOpen,
            'sidebar-mobile': isMobile,
            'sidebar-desktop': !isMobile
          }
        ]"
      >
        <div class="sidebar-content">
          <!-- 桌面端头部 -->
          <div v-if="!isMobile" class="desktop-header">
            <div class="flex items-center justify-between p-4">
              <h1 class="text-xl font-bold">{{ title }}</h1>
              <Hamburger :is-active="!sidebarCollapsed" @toggle="toggleSidebarCollapse" />
            </div>
          </div>
          
          <!-- 导航菜单 -->
          <nav class="sidebar-nav">
            <slot name="navigation" />
          </nav>
        </div>
      </aside>

      <!-- 主内容区域 -->
      <main 
        :class="[
          'main-content',
          {
            'main-content-mobile': isMobile,
            'main-content-desktop': !isMobile,
            'main-content-sidebar-open': sidebarOpen && !isMobile,
            'main-content-sidebar-collapsed': sidebarCollapsed && !isMobile
          }
        ]"
      >
        <!-- 桌面端头部工具栏 -->
        <header v-if="!isMobile" class="desktop-toolbar">
          <div class="flex items-center justify-between p-4 border-b">
            <Breadcrumb />
            <div class="flex items-center space-x-2">
              <Screenfull />
              <SizeSelect />
              <ThemeToggle />
              <LangSelect />
            </div>
          </div>
        </header>

        <!-- 内容区域 -->
        <div class="content-area">
          <slot />
        </div>

        <!-- 返回顶部按钮 -->
        <BackToTop />
      </main>
    </div>

    <!-- 移动端侧边栏遮罩 -->
    <div 
      v-if="isMobile && sidebarOpen"
      class="sidebar-overlay"
      @click="closeSidebar"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useBreakpoints } from '@/composables/useResponsive'
import Hamburger from '@/components/Hamburger/index.vue'
import Breadcrumb from '@/components/Breadcrumb/index.vue'
import BackToTop from '@/components/BackToTop/index.vue'
import Screenfull from '@/components/Screenfull/index.vue'
import SizeSelect from '@/components/SizeSelect/index.vue'
import ThemeToggle from '@/components/ThemeToggle/index.vue'
import LangSelect from '@/components/LangSelect/index.vue'

interface Props {
  title?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: 'Admin Dashboard'
})

const { isMobile, isTablet } = useBreakpoints()

const sidebarOpen = ref(!isMobile.value)
const sidebarCollapsed = ref(false)

const toggleSidebar = () => {
  sidebarOpen.value = !sidebarOpen.value
}

const closeSidebar = () => {
  sidebarOpen.value = false
}

const toggleSidebarCollapse = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

// 监听屏幕尺寸变化
const handleResize = () => {
  if (isMobile.value) {
    sidebarOpen.value = false
    sidebarCollapsed.value = false
  } else {
    sidebarOpen.value = true
  }
}

// 暴露方法给父组件
defineExpose({
  toggleSidebar,
  closeSidebar,
  toggleSidebarCollapse,
})
</script>

<style scoped>
.responsive-layout {
  @apply min-h-screen bg-background;
}

.mobile-header {
  @apply fixed top-0 left-0 right-0 z-50 bg-background border-b;
}

.layout-container {
  @apply flex;
  @apply md:pt-0;
  @apply pt-16; /* 为移动端头部留出空间 */
}

/* 侧边栏样式 */
.sidebar {
  @apply bg-card border-r transition-all duration-300 ease-in-out;
}

.sidebar-mobile {
  @apply fixed top-16 left-0 bottom-0 z-40;
  @apply w-64;
  transform: translateX(-100%);
}

.sidebar-mobile.sidebar-open {
  transform: translateX(0);
}

.sidebar-desktop {
  @apply relative;
  @apply w-64;
}

.sidebar-desktop.sidebar-closed {
  @apply w-16;
}

.sidebar-content {
  @apply h-full flex flex-col;
}

.desktop-header {
  @apply border-b;
}

.sidebar-nav {
  @apply flex-1 overflow-y-auto p-4;
}

/* 主内容区域样式 */
.main-content {
  @apply flex-1 flex flex-col min-h-screen;
}

.main-content-mobile {
  @apply w-full;
}

.main-content-desktop {
  @apply transition-all duration-300 ease-in-out;
}

.desktop-toolbar {
  @apply bg-background border-b;
}

.content-area {
  @apply flex-1 p-4;
  @apply md:p-6;
}

/* 遮罩层 */
.sidebar-overlay {
  @apply fixed inset-0 bg-black/50 z-30;
  @apply md:hidden;
}

/* 响应式网格布局工具类 */
.responsive-grid {
  @apply grid gap-4;
  @apply grid-cols-1;
  @apply sm:grid-cols-2;
  @apply lg:grid-cols-3;
  @apply xl:grid-cols-4;
}

.responsive-grid-auto {
  @apply grid gap-4;
  @apply grid-cols-1;
  @apply sm:grid-cols-2;
  @apply md:grid-cols-3;
  @apply lg:grid-cols-4;
  @apply xl:grid-cols-6;
}

/* 响应式间距 */
.responsive-spacing {
  @apply p-2;
  @apply sm:p-4;
  @apply md:p-6;
  @apply lg:p-8;
}

/* 响应式文字大小 */
.responsive-text {
  @apply text-sm;
  @apply sm:text-base;
  @apply md:text-lg;
  @apply lg:text-xl;
}

/* 响应式按钮组 */
.responsive-button-group {
  @apply flex gap-2;
  @apply flex-col;
  @apply sm:flex-row;
}

/* 响应式卡片 */
.responsive-card {
  @apply w-full;
  @apply max-w-sm;
  @apply sm:max-w-md;
  @apply md:max-w-lg;
  @apply lg:max-w-xl;
}
</style>
