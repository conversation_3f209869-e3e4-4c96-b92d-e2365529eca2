<script setup lang="ts">
import { computed } from 'vue'
import { useAppStore } from '@/store/app'
import { useUserStore } from '@/store/user'
import { useSettingsStore } from '@/store/settings'
import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Menu, Search, Maximize, Minimize, Sun, Moon, Globe, Bell, Settings, User, LogOut } from 'lucide-vue-next'
import ThemeToggle from '@/components/ThemeToggle/index.vue'

const appStore = useAppStore()
const userStore = useUserStore()
const settingsStore = useSettingsStore()

const sidebarCollapsed = computed(() => appStore.sidebar.collapsed)
const device = computed(() => appStore.device)
const currentUser = computed(() => userStore.userInfo)
const title = computed(() => settingsStore.title)

// 切换侧边栏
const toggleSidebar = () => {
  appStore.toggleSidebar()
}

// 全屏切换
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
  } else {
    document.exitFullscreen()
  }
}

// 用户登出
const handleLogout = () => {
  userStore.logout()
}
</script>

<template>
  <header class="h-16 bg-background border-b border-border flex items-center justify-between px-4 lg:px-6">
    <!-- 左侧区域 -->
    <div class="flex items-center gap-4">
      <!-- 侧边栏切换按钮 -->
      <Button
        variant="ghost"
        size="icon"
        @click="toggleSidebar"
        class="lg:hidden"
      >
        <Menu class="h-5 w-5" />
      </Button>
      
      <!-- Logo 和标题 -->
      <div class="flex items-center gap-3">
        <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
          <span class="text-primary-foreground font-bold text-sm">DTS</span>
        </div>
        <h1 class="text-xl font-semibold text-foreground hidden sm:block">
          {{ title }}
        </h1>
      </div>
    </div>

    <!-- 右侧功能区域 -->
    <div class="flex items-center gap-2">
      <!-- 搜索按钮 -->
      <Button variant="ghost" size="icon" class="hidden lg:flex">
        <Search class="h-5 w-5" />
      </Button>
      
      <!-- 全屏按钮 -->
      <Button
        variant="ghost"
        size="icon"
        @click="toggleFullscreen"
        class="hidden lg:flex"
      >
        <Maximize class="h-5 w-5" />
      </Button>
      
      <!-- 主题切换 -->
      <ThemeToggle />
      
      <!-- 通知按钮 -->
      <Button variant="ghost" size="icon" class="relative">
        <Bell class="h-5 w-5" />
        <Badge 
          variant="destructive" 
          class="absolute -top-1 -right-1 px-1 min-w-5 h-5 text-xs"
        >
          3
        </Badge>
      </Button>
      
      <!-- 用户下拉菜单 -->
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <Button variant="ghost" class="relative h-9 w-9 rounded-full">
            <Avatar class="h-9 w-9">
              <AvatarImage :src="currentUser?.avatar" :alt="currentUser?.username" />
              <AvatarFallback class="bg-primary text-primary-foreground">
                {{ currentUser?.username?.charAt(0)?.toUpperCase() || 'U' }}
              </AvatarFallback>
            </Avatar>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent class="w-56" align="end" :side-offset="4">
          <DropdownMenuLabel class="font-normal">
            <div class="flex flex-col space-y-1">
              <p class="text-sm font-medium leading-none">
                {{ currentUser?.username || '用户' }}
              </p>
              <p class="text-xs leading-none text-muted-foreground">
                {{ currentUser?.email || '<EMAIL>' }}
              </p>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem>
            <User class="mr-2 h-4 w-4" />
            <span>个人资料</span>
          </DropdownMenuItem>
          <DropdownMenuItem>
            <Settings class="mr-2 h-4 w-4" />
            <span>设置</span>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem @click="handleLogout" class="text-destructive">
            <LogOut class="mr-2 h-4 w-4" />
            <span>退出登录</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  </header>
</template>
