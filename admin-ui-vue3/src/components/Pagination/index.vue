<template>
  <div
    :class="{ 'hidden': hidden }"
    class="pagination-container"
  >
    <!-- 移动端布局 -->
    <div v-if="isMobile" class="space-y-4">
      <!-- 总数和每页条数 -->
      <div class="flex items-center justify-between text-sm">
        <div v-if="showTotal" class="text-muted-foreground">
          共 {{ total }} 条
        </div>
        <div v-if="showSizeChanger" class="flex items-center space-x-2">
          <span class="text-muted-foreground">每页</span>
          <Select v-model="pageSize">
            <SelectTrigger class="w-16 h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem
                v-for="size in pageSizes"
                :key="size"
                :value="size.toString()"
              >
                {{ size }}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <!-- 简化的分页控件 -->
      <div class="flex items-center justify-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          :disabled="currentPage <= 1"
          @click="goToPage(currentPage - 1)"
        >
          上一页
        </Button>
        <span class="text-sm px-3">
          {{ currentPage }} / {{ totalPages }}
        </span>
        <Button
          variant="outline"
          size="sm"
          :disabled="currentPage >= totalPages"
          @click="goToPage(currentPage + 1)"
        >
          下一页
        </Button>
      </div>
    </div>

    <!-- 桌面端布局 -->
    <div v-else class="flex items-center justify-between flex-wrap gap-4">
      <!-- 总数显示 -->
      <div v-if="showTotal" class="text-sm text-muted-foreground">
        共 {{ total }} 条记录
      </div>

      <!-- 分页组件 -->
      <Pagination
        v-model:page="currentPage"
        :total="total"
        :sibling-count="isTablet ? 0 : 1"
        :show-edges="!isTablet"
        class="mx-0"
      >
        <PaginationContent>
          <PaginationItem>
            <PaginationFirst @click="goToPage(1)" />
          </PaginationItem>
          <PaginationItem>
            <PaginationPrevious @click="goToPage(currentPage - 1)" />
          </PaginationItem>

          <!-- 页码 -->
          <template v-for="(page, index) in visiblePages" :key="index">
            <PaginationItem v-if="page === '...'" >
              <PaginationEllipsis />
            </PaginationItem>
            <PaginationItem v-else>
              <Button
                :variant="page === currentPage ? 'default' : 'outline'"
                size="icon"
                @click="goToPage(page)"
              >
                {{ page }}
              </Button>
            </PaginationItem>
          </template>

          <PaginationItem>
            <PaginationNext @click="goToPage(currentPage + 1)" />
          </PaginationItem>
          <PaginationItem>
            <PaginationLast @click="goToPage(totalPages)" />
          </PaginationItem>
        </PaginationContent>
      </Pagination>

      <!-- 每页条数选择 -->
      <div v-if="showSizeChanger" class="flex items-center space-x-2">
        <span class="text-sm text-muted-foreground">每页</span>
        <Select v-model="pageSize">
          <SelectTrigger class="w-20">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem
              v-for="size in pageSizes"
              :key="size"
              :value="size.toString()"
            >
              {{ size }}
            </SelectItem>
          </SelectContent>
        </Select>
        <span class="text-sm text-muted-foreground">条</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationFirst,
  PaginationItem,
  PaginationLast,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { scrollTo } from '@/utils/scrollTo'
import { useBreakpoints } from '@/composables/useResponsive'

interface Props {
  total: number
  page?: number
  limit?: number
  pageSizes?: number[]
  autoScroll?: boolean
  hidden?: boolean
  showTotal?: boolean
  showSizeChanger?: boolean
}

interface Emits {
  (e: 'update:page', page: number): void
  (e: 'update:limit', limit: number): void
  (e: 'pagination', data: { page: number; limit: number }): void
}

const props = withDefaults(defineProps<Props>(), {
  page: 1,
  limit: 20,
  pageSizes: () => [10, 20, 30, 50],
  autoScroll: true,
  hidden: false,
  showTotal: true,
  showSizeChanger: true,
})

const emit = defineEmits<Emits>()

const { isMobile, isTablet } = useBreakpoints()

const currentPage = computed({
  get: () => props.page,
  set: (val: number) => emit('update:page', val)
})

const pageSize = computed({
  get: () => props.limit.toString(),
  set: (val: string) => {
    const newLimit = parseInt(val)
    emit('update:limit', newLimit)
    emit('pagination', { page: currentPage.value, limit: newLimit })
    if (props.autoScroll) {
      scrollTo(0, 800)
    }
  }
})

const totalPages = computed(() => Math.ceil(props.total / props.limit))

const visiblePages = computed(() => {
  const pages: (number | string)[] = []
  const current = currentPage.value
  const total = totalPages.value
  
  if (total <= 7) {
    // 如果总页数小于等于7，显示所有页码
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    // 复杂的分页逻辑
    if (current <= 4) {
      for (let i = 1; i <= 5; i++) {
        pages.push(i)
      }
      pages.push('...')
      pages.push(total)
    } else if (current >= total - 3) {
      pages.push(1)
      pages.push('...')
      for (let i = total - 4; i <= total; i++) {
        pages.push(i)
      }
    } else {
      pages.push(1)
      pages.push('...')
      for (let i = current - 1; i <= current + 1; i++) {
        pages.push(i)
      }
      pages.push('...')
      pages.push(total)
    }
  }
  
  return pages
})

const goToPage = (page: number) => {
  if (page < 1 || page > totalPages.value || page === currentPage.value) {
    return
  }
  
  currentPage.value = page
  emit('pagination', { page, limit: props.limit })
  
  if (props.autoScroll) {
    scrollTo(0, 800)
  }
}
</script>

<style scoped>
.pagination-container {
  @apply bg-background p-8 border-t;
}

.pagination-container.hidden {
  @apply hidden;
}
</style>
