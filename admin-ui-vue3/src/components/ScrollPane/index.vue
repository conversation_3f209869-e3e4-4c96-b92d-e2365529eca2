<template>
  <ScrollArea
    ref="scrollContainer"
    class="scroll-container"
    orientation="horizontal"
    @wheel.prevent="handleScroll"
  >
    <div class="scroll-content">
      <slot />
    </div>
    <ScrollBar orientation="horizontal" />
  </ScrollArea>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area'

const tagAndTagSpacing = 4 // tagAndTagSpacing

const scrollContainer = ref<InstanceType<typeof ScrollArea>>()

const handleScroll = (e: WheelEvent) => {
  const eventDelta = e.deltaY * -40
  const scrollElement = scrollContainer.value?.$el?.querySelector('[data-radix-scroll-area-viewport]')
  
  if (scrollElement) {
    scrollElement.scrollLeft = scrollElement.scrollLeft + eventDelta / 4
  }
}

const moveToTarget = (currentTag: HTMLElement) => {
  const container = scrollContainer.value?.$el
  const scrollElement = container?.querySelector('[data-radix-scroll-area-viewport]')
  
  if (!container || !scrollElement) return
  
  const containerWidth = container.offsetWidth
  const tagList = container.querySelectorAll('.tag-item') // 假设标签有这个类名
  
  if (tagList.length === 0) return
  
  const tagArray = Array.from(tagList) as HTMLElement[]
  const currentIndex = tagArray.findIndex(tag => tag === currentTag)
  
  if (currentIndex === -1) return
  
  const firstTag = tagArray[0]
  const lastTag = tagArray[tagArray.length - 1]
  
  if (currentTag === firstTag) {
    scrollElement.scrollLeft = 0
  } else if (currentTag === lastTag) {
    scrollElement.scrollLeft = scrollElement.scrollWidth - containerWidth
  } else {
    const prevTag = tagArray[currentIndex - 1]
    const nextTag = tagArray[currentIndex + 1]
    
    if (nextTag) {
      const afterNextTagOffsetLeft = nextTag.offsetLeft + nextTag.offsetWidth + tagAndTagSpacing
      
      if (afterNextTagOffsetLeft > scrollElement.scrollLeft + containerWidth) {
        scrollElement.scrollLeft = afterNextTagOffsetLeft - containerWidth
      }
    }
    
    if (prevTag) {
      const beforePrevTagOffsetLeft = prevTag.offsetLeft - tagAndTagSpacing
      
      if (beforePrevTagOffsetLeft < scrollElement.scrollLeft) {
        scrollElement.scrollLeft = beforePrevTagOffsetLeft
      }
    }
  }
}

// 暴露方法给父组件使用
defineExpose({
  moveToTarget
})
</script>

<style scoped>
.scroll-container {
  @apply relative w-full overflow-hidden;
  white-space: nowrap;
}

.scroll-content {
  @apply flex items-center;
  height: 49px;
}

:deep([data-radix-scroll-area-viewport]) {
  height: 49px;
}

:deep([data-radix-scroll-area-scrollbar][data-orientation="horizontal"]) {
  bottom: 0;
}
</style>
