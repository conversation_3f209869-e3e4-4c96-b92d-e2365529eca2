<template>
  <Transition :name="transitionName">
    <Button
      v-show="visible"
      :style="customStyle"
      variant="outline"
      size="icon"
      class="back-to-ceiling"
      @click="backToTop"
    >
      <ArrowUp class="h-4 w-4" />
      <span class="sr-only">回到顶部</span>
    </Button>
  </Transition>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { Button } from '@/components/ui/button'
import { ArrowUp } from 'lucide-vue-next'

interface Props {
  visibilityHeight?: number
  backPosition?: number
  customStyle?: Record<string, string>
  transitionName?: string
}

const props = withDefaults(defineProps<Props>(), {
  visibilityHeight: 400,
  backPosition: 0,
  customStyle: () => ({
    position: 'fixed',
    right: '50px',
    bottom: '50px',
    zIndex: '1000'
  }),
  transitionName: 'fade'
})

const visible = ref(false)
const isMoving = ref(false)
let interval: number | null = null

const handleScroll = () => {
  visible.value = window.pageYOffset > props.visibilityHeight
}

const easeInOutQuad = (t: number, b: number, c: number, d: number): number => {
  if ((t /= d / 2) < 1) return c / 2 * t * t + b
  return -c / 2 * (--t * (t - 2) - 1) + b
}

const backToTop = () => {
  if (isMoving.value) return
  
  const start = window.pageYOffset
  let i = 0
  isMoving.value = true
  
  interval = window.setInterval(() => {
    const next = Math.floor(easeInOutQuad(10 * i, start, -start, 500))
    if (next <= props.backPosition) {
      window.scrollTo(0, props.backPosition)
      if (interval) clearInterval(interval)
      isMoving.value = false
    } else {
      window.scrollTo(0, next)
    }
    i++
  }, 16.7)
}

onMounted(() => {
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
  if (interval) {
    clearInterval(interval)
  }
})
</script>

<style scoped>
.back-to-ceiling {
  @apply transition-all duration-300 hover:scale-110;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
