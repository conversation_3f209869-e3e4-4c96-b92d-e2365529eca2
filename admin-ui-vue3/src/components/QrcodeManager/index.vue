<template>
  <Card class="qrcode-manager">
    <CardContent class="p-6">
      <!-- 二维码显示区域 -->
      <div class="qrcode-container">
        <div v-if="loading" class="flex items-center justify-center h-60">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
        
        <div v-else-if="qrcode" class="qrcode-content">
          <!-- 二维码图片 -->
          <div class="qrcode-image">
            <img
              v-if="qrcode.imageUrl"
              :src="qrcode.imageUrl"
              alt="二维码"
              :class="[
                'object-contain cursor-pointer',
                isMobile ? 'w-32 h-32' : 'w-48 h-48'
              ]"
              @click="previewImage"
            />
            <div v-else class="no-image">
              <QrCode :class="isMobile ? 'h-8 w-8' : 'h-12 w-12'" class="text-muted-foreground" />
              <p class="text-sm text-muted-foreground mt-2">暂无二维码图片</p>
            </div>
          </div>

          <!-- 二维码信息 -->
          <div class="qrcode-info">
            <div :class="isMobile ? 'space-y-2' : 'space-y-3'">
              <div class="info-item">
                <span class="label">类型：</span>
                <span class="value">{{ formatType(qrcode.type) }}</span>
              </div>
              <div class="info-item">
                <span class="label">关联ID：</span>
                <span class="value">{{ qrcode.relatedId }}</span>
              </div>
              <div class="info-item">
                <span class="label">场景值：</span>
                <span class="value">{{ qrcode.scene || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">状态：</span>
                <Badge :variant="qrcode.status === 0 ? 'default' : 'destructive'">
                  {{ qrcode.status === 0 ? '有效' : '无效' }}
                </Badge>
              </div>
              <div class="info-item">
                <span class="label">创建时间：</span>
                <span class="value">{{ qrcode.createTime }}</span>
              </div>
              <div v-if="qrcode.expireTime" class="info-item">
                <span class="label">过期时间：</span>
                <span class="value">{{ qrcode.expireTime }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <div v-else class="qrcode-empty">
          <QrCode class="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <p class="text-muted-foreground">暂无二维码信息，点击下方按钮创建二维码</p>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="qrcode-actions">
        <Button
          v-if="!qrcode"
          @click="generateQrcode"
          :disabled="generatingQrcode"
          :size="isMobile ? 'sm' : 'default'"
        >
          <Plus class="h-4 w-4 mr-2" />
          {{ generatingQrcode ? '生成中...' : '生成二维码' }}
        </Button>

        <template v-else>
          <Button
            v-if="qrcode.imageUrl"
            variant="outline"
            @click="downloadQrcode"
            :size="isMobile ? 'sm' : 'default'"
          >
            <Download class="h-4 w-4 mr-2" />
            <span v-if="!isMobile">下载二维码</span>
            <span v-else>下载</span>
          </Button>
          <Button
            variant="outline"
            @click="viewQrcodeDetail"
            :size="isMobile ? 'sm' : 'default'"
          >
            <Eye class="h-4 w-4 mr-2" />
            <span v-if="!isMobile">查看详情</span>
            <span v-else>详情</span>
          </Button>
          <Button
            variant="outline"
            @click="refreshQrcode"
            :size="isMobile ? 'sm' : 'default'"
          >
            <RefreshCw class="h-4 w-4 mr-2" />
            <span v-if="!isMobile">刷新</span>
            <span v-else>刷新</span>
          </Button>
        </template>
      </div>
    </CardContent>
    
    <!-- 图片预览对话框 -->
    <Dialog v-model:open="showPreview">
      <DialogContent class="max-w-md">
        <DialogHeader>
          <DialogTitle>二维码预览</DialogTitle>
        </DialogHeader>
        <div class="flex justify-center">
          <img
            v-if="qrcode?.imageUrl"
            :src="qrcode.imageUrl"
            alt="二维码预览"
            class="max-w-full h-auto"
          />
        </div>
      </DialogContent>
    </Dialog>
  </Card>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { QrCode, Plus, Download, Eye, RefreshCw } from 'lucide-vue-next'
import { toast } from '@/hooks/use-toast'
import { useBreakpoints } from '@/composables/useResponsive'

interface Props {
  type: string // 二维码类型：GOODS/BRAND/TOPIC/USER/GROUPON
  relatedId: number | string // 关联ID
  scene?: string // 场景值（可选）
  extra?: Record<string, any> // 额外参数
}

interface QrcodeData {
  id: number
  type: string
  relatedId: string
  scene: string
  imageUrl: string
  status: number
  createTime: string
  expireTime?: string
}

const props = withDefaults(defineProps<Props>(), {
  scene: '',
  extra: () => ({})
})

const router = useRouter()
const { isMobile } = useBreakpoints()

const loading = ref(false)
const generatingQrcode = ref(false)
const qrcode = ref<QrcodeData | null>(null)
const showPreview = ref(false)

// 获取二维码信息
const fetchQrcode = async () => {
  loading.value = true
  try {
    // 这里需要根据实际API调用
    // const response = await getQrcodeDetailByTypeAndId({
    //   type: props.type,
    //   relatedId: props.relatedId
    // })
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    qrcode.value = null // 暂时设为null，实际应该从API获取
    
  } catch (error) {
    toast({
      title: "获取二维码失败",
      description: "请稍后重试",
      variant: "destructive",
    })
    qrcode.value = null
  } finally {
    loading.value = false
  }
}

// 生成二维码
const generateQrcode = async () => {
  generatingQrcode.value = true
  try {
    const data = {
      type: props.type,
      relatedId: props.relatedId,
      scene: props.scene,
      ...props.extra
    }
    
    // 这里需要根据实际API调用
    // const response = await createQrcode(data)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 模拟生成的二维码数据
    qrcode.value = {
      id: 1,
      type: props.type,
      relatedId: props.relatedId.toString(),
      scene: props.scene,
      imageUrl: 'https://via.placeholder.com/200x200/000000/FFFFFF?text=QR+Code',
      status: 0,
      createTime: new Date().toLocaleString(),
    }
    
    toast({
      title: "成功",
      description: "二维码生成成功",
    })
  } catch (error) {
    toast({
      title: "生成二维码失败",
      description: "请稍后重试",
      variant: "destructive",
    })
  } finally {
    generatingQrcode.value = false
  }
}

// 下载二维码
const downloadQrcode = () => {
  if (!qrcode.value?.imageUrl) {
    toast({
      title: "提示",
      description: "二维码图片不存在",
      variant: "destructive",
    })
    return
  }
  
  const link = document.createElement('a')
  link.href = qrcode.value.imageUrl
  link.download = `qrcode_${props.type}_${props.relatedId}.png`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 查看二维码详情
const viewQrcodeDetail = () => {
  if (!qrcode.value) return
  
  router.push({
    path: '/promotion/qrcode',
    query: {
      id: qrcode.value.id.toString()
    }
  })
}

// 刷新二维码
const refreshQrcode = () => {
  fetchQrcode()
}

// 预览图片
const previewImage = () => {
  showPreview.value = true
}

// 格式化二维码类型
const formatType = (type: string) => {
  const typeMap: Record<string, string> = {
    'GOODS': '商品',
    'BRAND': '品牌',
    'TOPIC': '主题',
    'USER': '用户',
    'GROUPON': '团购'
  }
  return typeMap[type] || type
}

onMounted(() => {
  fetchQrcode()
})
</script>

<style scoped>
.qrcode-container {
  @apply min-h-60 flex justify-center items-center;
}

.qrcode-content {
  @apply flex w-full gap-6;
  @apply md:items-start md:flex-row;
  @apply flex-col items-center;
}

.qrcode-image {
  @apply flex justify-center items-center bg-muted border-2 border-dashed border-border rounded-lg;
  @apply md:w-48 md:h-48;
  @apply w-32 h-32;
}

.no-image {
  @apply flex flex-col items-center justify-center text-center;
}

.qrcode-info {
  @apply flex-1;
}

.info-item {
  @apply flex items-center;
}

.label {
  @apply text-muted-foreground mr-2 min-w-20;
}

.value {
  @apply text-foreground;
}

.qrcode-empty {
  @apply text-center;
}

.qrcode-actions {
  @apply mt-6 flex justify-center gap-2;
  @apply md:flex-row;
  @apply flex-col;
}
</style>
