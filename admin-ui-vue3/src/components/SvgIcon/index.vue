<template>
  <svg 
    :class="svgClass" 
    aria-hidden="true"
    :style="{ width: size, height: size }"
  >
    <use :href="iconName" />
  </svg>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  iconClass: string
  className?: string
  size?: string
}

const props = withDefaults(defineProps<Props>(), {
  className: '',
  size: '1em'
})

const iconName = computed(() => `#icon-${props.iconClass}`)

const svgClass = computed(() => {
  const baseClass = 'svg-icon'
  return props.className ? `${baseClass} ${props.className}` : baseClass
})
</script>

<style scoped>
.svg-icon {
  @apply inline-block align-baseline;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
</style>
