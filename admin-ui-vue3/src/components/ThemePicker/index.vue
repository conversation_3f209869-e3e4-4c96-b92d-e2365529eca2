<template>
  <Popover>
    <PopoverTrigger as-child>
      <Button variant="ghost" size="icon" class="theme-picker">
        <Palette class="h-5 w-5" />
        <span class="sr-only">选择主题颜色</span>
      </Button>
    </PopoverTrigger>
    <PopoverContent class="w-64" align="end">
      <div class="space-y-4">
        <div>
          <h4 class="font-medium leading-none mb-3">主题颜色</h4>
          <div class="grid grid-cols-4 gap-2">
            <button
              v-for="color in presetColors"
              :key="color.name"
              :class="[
                'w-8 h-8 rounded-md border-2 transition-all',
                currentColor === color.value
                  ? 'border-foreground scale-110 ring-2 ring-ring ring-offset-2'
                  : 'border-transparent hover:scale-105'
              ]"
              :style="{ backgroundColor: color.value }"
              @click="handleSetTheme(color.value)"
              :title="color.name"
            />
          </div>
        </div>

        <div>
          <label class="text-sm font-medium mb-2 block">自定义颜色</label>
          <input
            v-model="customColor"
            type="color"
            class="w-full h-10 rounded-md border border-input cursor-pointer"
            @change="handleSetTheme(customColor)"
          />
        </div>

        <div class="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            @click="handleResetTheme"
          >
            重置
          </Button>
          <Button
            variant="outline"
            size="sm"
            @click="toggleTheme"
          >
            {{ getThemeLabel() }}
          </Button>
        </div>
      </div>
    </PopoverContent>
  </Popover>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Button } from '@/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Palette } from 'lucide-vue-next'
import { toast } from '@/hooks/use-toast'
import { useTheme } from '@/composables/useTheme'

const {
  theme,
  colorScheme,
  setTheme,
  toggleTheme,
  setThemeColor,
  resetThemeColor,
  getCurrentThemeColor,
  presetColors,
} = useTheme()

const currentColor = ref(getCurrentThemeColor())
const customColor = ref(getCurrentThemeColor())

const handleSetTheme = (color: string) => {
  setThemeColor(color)
  currentColor.value = color
  customColor.value = color

  toast({
    title: "主题已更新",
    description: `已切换到新的主题颜色`,
  })
}

const handleResetTheme = () => {
  resetThemeColor()
  const defaultColor = presetColors[0].value
  currentColor.value = defaultColor
  customColor.value = defaultColor

  toast({
    title: "主题已重置",
    description: "已恢复为默认主题颜色",
  })
}

const getThemeLabel = () => {
  const labels = {
    light: '深色',
    dark: '浅色',
    system: '浅色'
  }
  return labels[theme.value] || '切换'
}

// 监听当前颜色变化
watch(() => getCurrentThemeColor(), (newColor) => {
  currentColor.value = newColor
  customColor.value = newColor
}, { immediate: true })
</script>


