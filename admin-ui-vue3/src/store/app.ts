import { defineStore } from 'pinia'

export interface SidebarState {
  collapsed: boolean
  openedMenus: string[]
}

export interface AppState {
  appName: string
  sidebar: SidebarState
  device: 'desktop' | 'tablet' | 'mobile'
  language: string
  size: string
}

// 兼容 pinia-plugin-persistedstate，persist 需为 true 或 { paths: [...] }
export const useAppStore = defineStore('app', {
  state: (): AppState => ({
    appName: 'DTS Shop Admin',
    sidebar: {
      collapsed: false,
      openedMenus: []
    },
    device: 'desktop',
    language: 'zh',
    size: 'medium'
  }),
  getters: {
    sidebarStatus: (state) => state.sidebar.collapsed ? 'collapsed' : 'expanded',
    appTitle: (state) => state.appName
  },
  actions: {
    toggleSidebar() {
      this.sidebar.collapsed = !this.sidebar.collapsed
    },
    closeSidebar(withoutAnimation: boolean = false) {
      this.sidebar.collapsed = true
    },
    openSidebar(withoutAnimation: boolean = false) {
      this.sidebar.collapsed = false
    },
    setSidebarOpenedMenus(openedMenus: string[]) {
      this.sidebar.openedMenus = openedMenus
    },
    setDevice(device: 'desktop' | 'tablet' | 'mobile') {
      this.device = device
    },
    setAppName(name: string) {
      this.appName = name
    },
    setLanguage(language: string) {
      this.language = language
    },
    setSize(size: string) {
      this.size = size
    }
  },
  persist: {
    pick: ['sidebar.collapsed', 'sidebar.openedMenus', 'language', 'size']
  }
})
