import { createI18n } from 'vue-i18n'

// 中文语言包
const zhCN = {
  route: {
    dashboard: '首页',
    showcase: '组件展示',
    system: '系统管理',
    user: '用户管理',
    role: '角色管理',
    business: '商城管理',
    goods: '商品管理',
    order: '订单管理'
  },
  common: {
    confirm: '确认',
    cancel: '取消',
    save: '保存',
    delete: '删除',
    edit: '编辑',
    add: '添加',
    search: '搜索',
    reset: '重置',
    submit: '提交',
    back: '返回',
    loading: '加载中...',
    success: '操作成功',
    error: '操作失败',
    warning: '警告',
    info: '提示'
  },
  theme: {
    light: '浅色模式',
    dark: '深色模式',
    system: '跟随系统',
    colorChanged: '主题颜色已更新',
    modeChanged: '主题模式已切换'
  },
  language: {
    zh: '中文',
    en: 'English',
    changed: '语言切换成功'
  },
  size: {
    large: '大',
    medium: '中',
    small: '小',
    changed: '尺寸切换成功'
  }
}

// 英文语言包
const enUS = {
  route: {
    dashboard: 'Dashboard',
    showcase: 'Component Showcase',
    system: 'System',
    user: 'User Management',
    role: 'Role Management',
    business: 'Business',
    goods: 'Goods Management',
    order: 'Order Management'
  },
  common: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    add: 'Add',
    search: 'Search',
    reset: 'Reset',
    submit: 'Submit',
    back: 'Back',
    loading: 'Loading...',
    success: 'Success',
    error: 'Error',
    warning: 'Warning',
    info: 'Info'
  },
  theme: {
    light: 'Light Mode',
    dark: 'Dark Mode',
    system: 'Follow System',
    colorChanged: 'Theme color updated',
    modeChanged: 'Theme mode switched'
  },
  language: {
    zh: '中文',
    en: 'English',
    changed: 'Language switched successfully'
  },
  size: {
    large: 'Large',
    medium: 'Medium',
    small: 'Small',
    changed: 'Size switched successfully'
  }
}

// 创建i18n实例
const i18n = createI18n({
  legacy: false, // 使用 Composition API
  locale: 'zh', // 默认语言
  fallbackLocale: 'zh', // 回退语言
  messages: {
    zh: zhCN,
    en: enUS
  }
})

export default i18n

// 导出类型
export type MessageSchema = typeof zhCN
