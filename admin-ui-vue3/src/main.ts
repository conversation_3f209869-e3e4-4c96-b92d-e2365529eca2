import { initializeResponsive } from '@/composables/useResponsive'
import { initializeTheme } from '@/composables/useTheme'
import { setI18nInstance } from '@/utils/i18n'
import { createApp } from 'vue'
import App from './App.vue'
import './assets/globals.css'
import i18n from './i18n'
import router from './router'
import pinia from './store'

const app = createApp(App)
app.use(pinia)
app.use(router)
app.use(i18n)

// 初始化i18n系统
setI18nInstance(i18n.global)

// 初始化主题系统
initializeTheme()

// 初始化响应式系统
initializeResponsive()

app.mount('#app')
